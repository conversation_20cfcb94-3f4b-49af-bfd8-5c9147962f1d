# 班級作業追蹤系統 - CLAUDE.md

## 🎯 專案概述
這是一個專為國小班級設計的現代化網頁版作業追蹤系統，採用純前端技術開發，具備完整的作業管理、學生管理、統計分析和加分獎勵功能。系統採用響應式設計，支援桌面、平板和手機使用，資料儲存於瀏覽器 localStorage，無需後端伺服器即可運行。

## 📅 最新更新記錄

### v1.5 (2025-07-30) - 提交時間追蹤更新
- **⏰ 提交時間記錄**: 自動記錄每個科目的作業提交時間戳記
- **🕐 智能時間顯示**: 今日作業顯示時間，歷史作業顯示完整日期時間
- **📊 時間資訊整合**: 在狀態網格中展示每個科目的提交時間
- **🔄 資料結構升級**: 從布林值升級為物件格式，向下兼容舊資料
- **✏️ 編輯功能增強**: 編輯時智能處理時間戳記的保留和更新
- **📥 下載時間詳情**: CSV/Excel下載包含完整的年月日時分秒提交時間
- **⏱️ 自動儲存優化**: 儲存間隔調整為每3分鐘，減少系統負載

### v1.4 (2025-07-30) - 加分系統重大更新
- **⭐ 全新加分系統**: 完整的學生獎勵機制，支援按日期和作業完成度加分
- **🎯 智能評分**: 自動根據學生完成的作業科目數量計算加分（可自訂分值）
- **📊 進階統計**: 學生加分排行榜、趨勢分析、多維度統計報表
- **📥 豐富下載**: 支援加分記錄下載（CSV、Excel、JSON）和統計報告匯出
- **🔧 系統穩定**: 修復JavaScript函數截斷問題，提升系統穩定性
- **📱 界面優化**: 全新加分系統界面，保持與原有風格一致的設計

### v1.3 (2024-01-30) - 功能完善更新
- **🔧 重要修復**: 解決學生作業提交影響全部學生的關鍵bug
- **✏️ 編輯功能**: 新增學生資料修改功能（座號、姓名），支援歷史記錄更新
- **📊 狀態升級**: 詳細顯示每位學生的已交/未交科目狀態
- **🎯 視覺優化**: 新增圓形座號徽章，雙重顯示座號資訊
- **📥 下載擴充**: 支援多格式記錄下載（CSV、Excel、JSON）

### v1.2 - 基礎功能建立
- **📚 核心功能**: 作業登記、學生管理、歷史記錄查詢
- **🏆 排行榜系統**: 學生完成率排名和激勵機制
- **📱 響應式設計**: 完整的桌面、平板、手機端適配

## 🏗️ 系統架構

### 📁 檔案結構
```
作業登記第二版全班/
├── index.html          # 主要HTML結構，包含所有頁面模板
├── styles.css          # CSS樣式檔案，響應式設計
├── script.js           # JavaScript功能實作，包含所有業務邏輯
└── CLAUDE.md          # 專案說明文件（本檔案）
```

### 🔧 技術棧
- **前端框架**: 原生HTML5, CSS3, JavaScript (ES6+)
- **資料儲存**: localStorage (瀏覽器本地儲存)
- **設計原則**: 響應式設計、觸控友善、無障礙設計
- **相容性**: 支援現代瀏覽器 (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+)
- **部署方式**: 靜態檔案，可直接開啟或部署至任何網頁伺服器

### 📊 資料架構
- **學生資料**: 座號、姓名、統計資訊
- **作業記錄**: 日期、科目、繳交狀態
- **加分記錄**: 日期、學生、加分值、完成科目
- **系統設定**: 科目列表、系統偏好設定

## ⚡ 核心功能模組

### 1. 📝 作業登記系統 (Homework Entry)
**老師端功能:**
- ➕ 新增每日作業科目（支援自訂科目名稱）
- ➖ 移除不需要的科目
- 📋 即時查看全班繳交狀況
- 🎨 視覺化狀態顯示（綠色：全部完成、黃色：部分完成、紅色：未繳交）
- ✏️ 編輯學生繳交狀態，支援個別修正

**學生端功能:**
- 🎯 選擇個人座號
- ✅ 勾選已完成的作業科目
- 📤 一鍵提交作業狀態
- 🎉 完成後顯示隨機鼓勵訊息

**系統特色:**
- 🔄 即時更新狀態網格
- 🎪 豐富的鼓勵訊息庫（10+ 條激勵話語）
- 🛡️ 防重複提交機制
- **⏰ 提交時間記錄 (v1.5新增)**: 
  - 自動記錄每個科目的提交時間戳記
  - 智能時間顯示（今日顯示時間，歷史顯示日期時間）
  - 編輯功能智能處理時間戳記的保留和更新

### 2. 👥 學生名單管理 (Student Management)
**基本管理功能:**
- ➕ 新增學生（座號 + 姓名，支援1-50號座號）
- ➖ 移除學生（含確認對話框防誤刪）
- ✏️ 編輯學生資料（座號、姓名），自動更新歷史記錄
- 🔍 即時搜尋功能（支援座號或姓名模糊搜尋）

**視覺化功能:**
- 🎯 圓形座號徽章 + 文字標籤雙重顯示
- 📊 每位學生的完成率和繳交次數統計
- 🎨 美觀的學生卡片式佈局
- 📱 響應式網格，適配各種螢幕尺寸

### 3. 📊 作業記錄查詢 (History Management)
**查詢功能:**
- 📅 依日期查看歷史作業記錄
- 🔍 快速日期選擇和篩選
- 📋 顯示當日作業科目和繳交狀況
- 🎯 學生個別完成狀態一目了然

**下載功能:**
- 📥 單日記錄下載（CSV、Excel、JSON格式）
- 📥 全部歷史記錄下載
- 📈 包含統計摘要資訊（完成率、參與度等）
- 🎨 支援中文字符編碼（UTF-8 BOM）

### 4. 🏆 排行榜系統 (Leaderboard)
**排名機制:**
- 🥇 依作業完成率排序前5名學生
- 🏅 動態更新排行榜（即時計算）
- 📊 顯示完成百分比和具體數據
- 🎖️ 使用獎牌圖示增加視覺吸引力

**激勵功能:**
- 🌟 突出顯示優秀學生
- 📈 鼓勵學生提升完成率
- 🎯 公平競爭環境

### 5. ⭐ 加分系統 (Bonus Points System) - v1.4 全新功能
**加分設定:**
- 📅 選擇特定日期進行加分
- 🎯 自訂每份作業加分值（預設5分，可調整1-20分）
- 📅 支援追溯加分（可為過往日期加分）
- 🔄 即時載入該日期的作業記錄

**評分機制:**
- 🤖 智能評分：自動根據完成的作業科目數量計算總加分
- 👤 個別加分：為單一學生手動加分或取消加分
- 🎖️ 批量加分：一鍵為所有符合條件的學生加分
- ✅ 防重複加分機制

**統計分析:**
- 🏆 學生加分排行榜（按總加分排序）
- 📊 多維度統計：
  - 每位學生的總加分、加分天數、平均加分
  - 完成作業總數統計
  - 按日期的加分分佈
- 📈 趨勢分析和比較功能

**下載匯出:**
- 📥 單日加分記錄下載（CSV、Excel、JSON）
- 📥 全部加分記錄下載
- 📊 統計報告下載（包含排行榜和趨勢分析）
- 🎨 格式化報表，適合列印和分享

## 💾 資料結構與儲存

### 📊 localStorage 儲存格式
系統使用瀏覽器的 localStorage 進行資料持久化，所有資料以 JSON 格式儲存：

```javascript
// 學生基本資料
students: [
  {
    seatNumber: 1,           // 座號（字串格式以避免型別問題）
    name: "王小明",          // 學生姓名
    totalSubmissions: 10,    // 總繳交次數
    totalHomework: 12        // 總作業次數（用於計算完成率）
  },
  {
    seatNumber: 2,
    name: "李小華", 
    totalSubmissions: 8,
    totalHomework: 12
  }
]

// 科目列表（可動態新增）
subjects: ["國語", "數學", "英語", "自然", "社會"]

// 作業記錄（以日期為鍵值的嵌套物件）- v1.5 升級格式
currentHomework: {
  "2025-07-30": {
    subjects: ["國語", "數學", "英語"],    // 當日作業科目
    submissions: {                        // 學生繳交狀況（新格式）
      "1": { 
        "國語": { 
          completed: true, 
          timestamp: "2025-07-30T05:30:00.000Z" 
        }, 
        "數學": { 
          completed: false, 
          timestamp: null 
        }, 
        "英語": { 
          completed: true, 
          timestamp: "2025-07-30T06:15:00.000Z" 
        }
      },
      "2": { 
        "國語": { 
          completed: true, 
          timestamp: "2025-07-30T05:35:00.000Z" 
        }, 
        "數學": { 
          completed: true, 
          timestamp: "2025-07-30T05:40:00.000Z" 
        }, 
        "英語": { 
          completed: false, 
          timestamp: null 
        }
      }
    }
  },
  "2025-07-29": {
    subjects: ["國語", "數學"],
    submissions: {
      // 系統向下兼容舊格式資料
      "1": { "國語": false, "數學": true },  // 舊格式（布林值）
      "2": { 
        "國語": { 
          completed: true, 
          timestamp: "2025-07-29T14:20:00.000Z" 
        }, 
        "數學": { 
          completed: true, 
          timestamp: "2025-07-29T14:25:00.000Z" 
        }
      }  // 新格式（物件）
    }
  }
}

// 加分記錄（v1.4新增）- 以日期為主鍵，座號為次鍵
bonusPoints: {
  "2025-07-30": {
    "1": { 
      points: 10,                                    // 獲得的總加分
      subjects: ["國語", "英語"],                    // 完成的作業科目
      timestamp: "2025-07-30T10:30:00.000Z"        // 加分時間戳記
    },
    "2": { 
      points: 15, 
      subjects: ["國語", "數學", "英語"], 
      timestamp: "2025-07-30T10:35:00.000Z" 
    }
  },
  "2025-07-29": {
    "1": { 
      points: 10, 
      subjects: ["國語", "數學"], 
      timestamp: "2025-07-29T14:20:00.000Z" 
    }
  }
}
```

### 🔄 資料同步機制
- **自動儲存**: 每3分鐘自動儲存至 localStorage
- **即時更新**: 使用者操作後立即儲存變更
- **頁面關閉保護**: beforeunload 事件確保資料不遺失
- **資料驗證**: 載入時自動檢查資料完整性和格式正確性
- **格式相容**: v1.5 支援新舊資料格式的無縫轉換和相容性處理

## 🎨 設計特色與使用者體驗

### 1. 👶 兒童友善設計
**視覺設計:**
- 🌈 溫暖的粉色、紫色漸層背景，營造友善學習環境
- 🎪 豐富的 emoji 圖示增加趣味性和視覺引導
- ✨ 大字體設計，確保兒童易於閱讀
- 🎯 圓角按鈕設計，觸控友善（最小44px符合無障礙標準）
- 🏷️ 醒目的圓形座號徽章，快速識別學生身份

**互動體驗:**
- 🎊 完成作業時的脈衝動畫效果
- 🎉 隨機鼓勵訊息系統（10+條激勵話語）
- 🎭 狀態色彩區分：綠色（完成）、黃色（部分完成）、紅色（未完成）
- 🚀 流暢的頁面切換動畫

### 2. 📱 響應式設計
**桌面版 (>1024px):**
- 📊 三欄式佈局：側邊欄導航 + 主內容區 + 排行榜
- 💻 充分利用大螢幕空間顯示更多資訊
- 🖱️ 懸停效果和豐富的互動回饋

**平板版 (768px-1024px):**
- 📐 調整側邊欄寬度，優化觸控體驗
- 🎯 保持核心功能完整性
- 👆 增大觸控目標尺寸

**手機版 (<768px):**
- 📱 垂直堆疊佈局，單欄顯示
- 🎮 隱藏次要文字，僅保留圖示導航
- 👍 針對單手操作優化按鈕位置

### 3. 🔧 系統互動設計
**即時回饋系統:**
- ⚡ 操作後立即顯示視覺回饋
- 📢 彈出式通知系統（成功/錯誤訊息）
- 🔄 載入狀態指示
- ✅ 表單驗證即時提示

**無障礙設計:**
- ♿ 支援鍵盤導航（Tab鍵遍歷）
- 🔤 適當的對比度確保可讀性
- 🏷️ 語意化HTML結構
- 📱 螢幕閱讀器友善

## 📖 使用指南

### 🚀 初次設定流程
1. **開啟系統**: 直接開啟 `index.html` 檔案或部署至網頁伺服器
2. **建立學生名單**: 
   - 點選左側導航「👥 學生名單」
   - 逐一新增班上學生（輸入座號和姓名）
   - 可使用搜尋功能快速查找學生
3. **設定科目**: 回到「📝 登記作業」頁面，新增當日作業科目
4. **系統就緒**: 現在可以開始使用各項功能

### 📅 日常操作工作流程
**老師端操作:**
1. **新增作業**: 每日在「老師區域」新增當天的作業科目
2. **監控狀況**: 即時查看「今日繳交狀況」網格
3. **獎勵學生**: 使用「⭐ 加分系統」為按時完成作業的學生加分
4. **資料分析**: 查看「🏆 排行榜」和加分統計

**學生端操作:**
1. **選擇座號**: 在下拉選單中選擇自己的座號
2. **勾選作業**: 勾選已完成的作業科目
3. **提交確認**: 點擊「提交作業」按鈕
4. **查看結果**: 系統顯示鼓勵訊息和個人狀態
5. **時間記錄**: 系統自動記錄每個科目的提交時間（v1.5新增）

**時間資訊查看:**
- 📊 在「今日繳交狀況」中查看每個科目的提交時間
- 🕐 今日作業顯示具體時間（如：下午02:30）
- 📅 歷史作業顯示完整日期時間（如：7月29日 下午02:30）
- ✏️ 編輯作業狀態時，系統智能處理時間戳記

### ⭐ 加分系統使用指南 (v1.4)
**基本操作:**
1. **選擇日期**: 在加分設定區選擇要加分的日期
2. **載入資料**: 點擊「📅 載入日期」載入該日的作業記錄
3. **設定分值**: 調整每份作業的加分值（1-20分）
4. **執行加分**: 
   - 🎯 個別加分：為特定學生點擊「⭐ 加分」
   - 🎖️ 批量加分：點擊「⭐ 全部加分」為所有符合條件的學生加分

**統計查看:**
- 📊 即時查看學生加分排行榜
- 📈 分析各日期的加分分佈情況
- 🏆 比較學生間的表現差異

**資料匯出:**
- 📥 下載單日加分記錄進行備份
- 📊 匯出統計報告供學期評量參考
- 🎨 選擇適合的檔案格式（CSV適合Excel、JSON適合程式處理）

### 💾 資料管理與備份
- **自動儲存**: 每3分鐘自動儲存至 localStorage，頁面關閉前強制儲存
- **手動匯出**: 開發者工具執行 `HomeworkSystem.exportData()` 匯出完整資料
- **備份建議**: 定期下載記錄檔案進行備份，避免瀏覽器資料遺失
- **資料清理**: 開發者工具執行 `HomeworkSystem.clearAllData()` 清空所有資料（請謹慎使用）

## ⌨️ 鍵盤快捷鍵
為提升操作效率，系統支援以下快捷鍵：
- `Alt + 1`: 切換至「📝 登記作業」頁面
- `Alt + 2`: 切換至「👥 學生名單」頁面  
- `Alt + 3`: 切換至「📊 作業記錄」頁面
- `Alt + 4`: 切換至「⭐ 加分系統」頁面

## 🔧 開發者工具與除錯

### 💻 控制台命令
在瀏覽器開發者工具的控制台中，可使用以下命令進行系統管理：

```javascript
// === 資料匯出相關 ===
HomeworkSystem.exportData()        // 匯出所有系統資料（JSON格式）

// === 記錄下載功能 ===
downloadSingleRecord()             // 下載選定日期的作業記錄
downloadAllRecords()               // 下載全部作業記錄

// === 加分系統功能（v1.4新增）===
downloadSingleBonusRecord()        // 下載選定日期的加分記錄
downloadAllBonusRecords()          // 下載全部加分記錄
downloadBonusSummaryReport()       // 下載加分統計報告

// === 系統查詢功能 ===
HomeworkSystem.students()          // 查看所有學生資料
HomeworkSystem.stats()             // 查看系統統計資料（包含加分統計）
console.table(currentStudents)     // 以表格形式顯示學生列表
console.table(bonusPoints)         // 以表格形式顯示加分記錄

// === 時間戳記相關功能（v1.5新增）===
formatSubmissionTime(timestamp)    // 格式化時間戳記顯示
console.table(currentHomework)     // 查看完整作業記錄（包含時間戳記）

// === 系統管理功能 ===
HomeworkSystem.clearAllData()      // 清空所有資料（⚠️ 危險操作，無法復原）
localStorage.clear()               // 清空瀏覽器所有 localStorage 資料
```

### 🔍 除錯與排除問題
**常見問題解決:**
1. **資料遺失**: 檢查瀏覽器是否清理了 localStorage，建議定期備份
2. **功能異常**: 按 F12 開啟開發者工具查看控制台錯誤訊息
3. **樣式錯誤**: 確認 CSS 檔案正確載入，檢查瀏覽器相容性
4. **效能問題**: 大量資料時建議清理舊記錄，或使用分頁功能

## 🌐 瀏覽器支援與相容性
**建議瀏覽器版本:**
- **Chrome**: 60+ (建議使用最新版本)
- **Firefox**: 55+ (支援 ES6+ 語法)
- **Safari**: 12+ (Mac/iOS 完整支援)
- **Edge**: 79+ (Chromium 版本)

**技術需求:**
- ✅ JavaScript ES6+ 支援
- ✅ localStorage API 支援
- ✅ CSS Grid 和 Flexbox 支援
- ✅ 安全環境（HTTPS 或本機）以確保 localStorage 正常運作

## 🚀 部署與安裝指南
**本機使用:**
1. 下載所有檔案至同一資料夾
2. 直接雙擊開啟 `index.html`
3. 開始使用系統

**網頁伺服器部署:**
1. 將檔案上傳至網頁伺服器目錄
2. 設定靜態檔案服務
3. 確保 HTTPS 協定以獲得最佳安全性
4. 訪問 index.html 開始使用

**Docker 部署（可選）:**
```dockerfile
FROM nginx:alpine
COPY . /usr/share/nginx/html
EXPOSE 80
```

## 🔮 未來發展規劃

### 📋 短期計劃 (v1.5)
1. **作業內容描述**: 支援為每項作業添加詳細說明
2. **批量學生匯入**: 支援 CSV 檔案批量匯入學生名單
3. **家長查看模式**: 唯讀模式供家長查看孩子作業狀況
4. **多語言支援**: 增加英文界面選項

### 🎯 中期目標 (v2.0)
1. **多班級管理**: 支援同時管理多個班級
2. **雲端同步**: 整合 Google Drive 或其他雲端儲存服務
3. **進階統計**: 包含圖表和趨勢分析的統計報表
4. **電子郵件通知**: 自動發送作業提醒給家長

### 🌟 長期願景 (v3.0)
1. **行動應用**: 開發原生手機 APP
2. **人工智慧**: 智能分析學生學習模式和建議
3. **社群功能**: 班級討論區和互動功能
4. **與學校系統整合**: 支援匯出至校務系統

### ⭐ 加分系統擴充計劃
**進階功能規劃:**
- 🏆 **多元加分類型**: 支援課業、品德、才藝等不同類型加分
- 📈 **視覺化圖表**: 加分趨勢圖、比較圖表
- 🎖️ **徽章系統**: 達成特定目標時頒發數位徽章
- 📊 **智能分析**: AI 分析學生學習模式和建議
- 👨‍👩‍👧‍👦 **家長端查看**: 讓家長能查看孩子的加分記錄
- 🔔 **提醒通知**: 定期提醒老師進行加分評估

## ⚠️ 注意事項與限制

### 🚨 重要提醒
- **資料備份**: localStorage 資料可能因瀏覽器清理而遺失，請定期備份
- **隱私保護**: 系統僅在本機運行，但請注意保護學生個人資料
- **效能限制**: localStorage 有容量限制（約5-10MB），大量資料時需要清理
- **網路需求**: 純前端系統，無網路也可正常使用

### 🔒 資料安全
- 所有資料儲存在使用者的瀏覽器中，不會上傳至任何伺服器
- 建議在專用電腦或平板上使用，避免資料外洩
- 定期更新瀏覽器以確保安全性

### 📞 技術支援
- 遇到問題請檢查瀏覽器控制台的錯誤訊息
- 確認瀏覽器版本符合系統需求
- 可參考本文件的除錯章節進行排除

## 📄 授權與版權
此專案為教育用途開發，採開源精神分享：
- ✅ **教育使用**: 學校和教師可免費使用和修改
- ✅ **非商業用途**: 個人和教育機構可自由使用
- ❌ **商業用途**: 如需商業使用請聯繫開發者
- 🔄 **貢獻歡迎**: 歡迎提供改進建議和程式碼貢獻

---

## 📝 更新日誌詳細記錄

**v1.5 (2025-07-30) 詳細變更:**
- ➕ 新增提交時間戳記記錄功能（約200行程式碼）
- 🔄 升級資料結構從布林值到物件格式，向下兼容
- 📊 重構狀態顯示系統以支援時間資訊展示
- ✏️ 增強編輯功能的時間戳記智能處理
- 🎨 新增時間顯示相關CSS樣式
- 📚 完善文件說明和使用指南

**v1.4 (2025-07-30) 詳細變更:**
- ➕ 新增完整的加分系統模組（約600行程式碼）
- 🔧 修復 JavaScript 函數截斷問題
- 📊 加入多維度統計分析功能
- 📥 擴充下載功能支援加分記錄
- 📱 優化響應式界面適配
- 📚 完善文件說明和使用指南

**系統統計資料 (v1.5):**
- 總程式碼行數: ~1,900行 JavaScript + 850行 CSS + 300行 HTML
- 功能模組: 5個主要模組，25+個子功能
- 支援檔案格式: CSV, Excel, JSON
- 資料儲存容量: 支援 5MB+ 資料量
- 響應式斷點: 3個主要斷點適配不同設備
- 時間精度: 毫秒級時間戳記記錄

---
*最後更新: 2025年7月30日 - 班級作業追蹤系統 v1.5*