/* 全域樣式設定 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', '微軟正黑體', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    background: linear-gradient(135deg, #FFE5B4 0%, #FFD1DC 50%, #E6E6FA 100%);
    min-height: 100vh;
    color: #333;
}

/* 主容器 */
.container {
    display: flex;
    min-height: 100vh;
    max-width: 1400px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin: 10px;
    overflow: hidden;
}

/* 左側邊欄 */
.sidebar {
    width: 280px;
    background: linear-gradient(180deg, #FF9A9E 0%, #FECFEF 50%, #FECFEF 100%);
    padding: 20px;
    border-right: 3px solid #FF69B4;
    position: relative;
    overflow-y: auto;
}

.logo h2 {
    color: #8B008B;
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* 導航選單 */
.nav-menu {
    list-style: none;
    margin-bottom: 30px;
}

.nav-item {
    margin-bottom: 15px;
}

.nav-btn {
    width: 100%;
    background: rgba(255, 255, 255, 0.8);
    border: none;
    padding: 15px 20px;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
    color: #8B008B;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.nav-item.active .nav-btn {
    background: #FFD700;
    color: #8B008B;
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

.nav-btn .icon {
    margin-right: 10px;
    font-size: 20px;
}

/* 排行榜 */
.leaderboard {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 15px;
    margin-top: 20px;
}

.leaderboard h3 {
    color: #8B008B;
    text-align: center;
    margin-bottom: 15px;
    font-size: 18px;
}

.leaderboard-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 8px;
    background: linear-gradient(90deg, #FFE4E1, #F0F8FF);
    border-radius: 10px;
    font-size: 14px;
    font-weight: bold;
}

.leaderboard-rank {
    color: #FF69B4;
}

.leaderboard-score {
    color: #32CD32;
}

/* 主內容區域 */
.main-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    background: linear-gradient(135deg, #F0F8FF 0%, #E6E6FA 100%);
}

/* 內容區塊 */
.content-section {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 區塊標題 */
.section-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(90deg, #FFB6C1, #DDA0DD);
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    position: relative; /* 新增相對定位，供重置按鈕使用 */
}

.section-header h1 {
    color: #8B008B;
    font-size: 32px;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.date-display {
    font-size: 18px;
    color: #666;
    font-weight: bold;
}

/* 低調的系統重置按鈕 */
.btn-reset-subtle {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255, 255, 255, 0.3);
    color: #666;
    border: 1px solid rgba(255, 255, 255, 0.5);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 13px;
    font-weight: bold;
    transition: all 0.3s ease;
    opacity: 0.7;
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    white-space: nowrap;
}

.btn-reset-subtle:hover {
    background: rgba(244, 67, 54, 0.8);
    color: white;
    opacity: 1;
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.btn-reset-subtle:active {
    transform: scale(0.95);
}

/* 表單樣式 */
.teacher-section, .student-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 3px solid #FFB6C1;
}

.teacher-section h2, .student-section h2 {
    color: #8B008B;
    margin-bottom: 20px;
    font-size: 24px;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #8B008B;
    font-size: 18px;
}

.form-group input, .form-group select {
    width: 100%;
    padding: 15px;
    border: 3px solid #FFB6C1;
    border-radius: 15px;
    font-size: 18px;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.form-group input:focus, .form-group select:focus {
    outline: none;
    border-color: #FF69B4;
    box-shadow: 0 0 10px rgba(255, 105, 180, 0.3);
    transform: scale(1.02);
}

.form-row .form-group {
    flex: 1;
    min-width: 150px;
}

/* 按鈕樣式 */
.btn-primary, .btn-success, .btn-danger {
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    min-height: 50px;
}

.btn-primary {
    background: linear-gradient(45deg, #FF69B4, #FF1493);
    color: white;
}

.btn-success {
    background: linear-gradient(45deg, #32CD32, #228B22);
    color: white;
}

.btn-danger {
    background: linear-gradient(45deg, #FF6347, #DC143C);
    color: white;
}

.btn-primary:hover, .btn-success:hover, .btn-danger:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.btn-primary:disabled, .btn-success:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* 科目列表 */
.subjects-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 20px;
}

.subject-item {
    background: linear-gradient(45deg, #FFE4E1, #F0F8FF);
    padding: 15px 20px;
    border-radius: 20px;
    border: 2px solid #FFB6C1;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
    font-weight: bold;
    color: #8B008B;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.subject-remove {
    background: #FF6347;
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 作業清單 */
.homework-checklist {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.homework-item {
    background: linear-gradient(45deg, #F0F8FF, #E6E6FA);
    padding: 20px;
    border-radius: 15px;
    border: 2px solid #DDA0DD;
    text-align: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.homework-item label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    color: #8B008B;
}

.homework-item input[type="checkbox"] {
    width: 25px;
    height: 25px;
    accent-color: #FF69B4;
}

/* 狀態顯示 */
.status-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 3px solid #98FB98;
}

.status-section h3 {
    color: #8B008B;
    text-align: center;
    margin-bottom: 20px;
    font-size: 24px;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

/* 詳細狀態項目 */
.status-item-detailed {
    padding: 15px;
    border-radius: 15px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    border: 2px solid;
    transition: transform 0.3s ease;
}

.status-item-detailed:hover {
    transform: translateY(-2px);
}

.status-item-detailed.completed {
    background: linear-gradient(45deg, #98FB98, #90EE90);
    border-color: #32CD32;
    color: #006400;
}

.status-item-detailed.partial {
    background: linear-gradient(45deg, #FFE4B5, #F0E68C);
    border-color: #DAA520;
    color: #8B4513;
}

.status-item-detailed.pending {
    background: linear-gradient(45deg, #FFB6C1, #FFA0B4);
    border-color: #FF69B4;
    color: #8B0000;
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-weight: bold;
    font-size: 16px;
}

.student-info {
    flex: 1;
}

.status-count {
    background: rgba(255, 255, 255, 0.8);
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: bold;
}

/* 編輯狀態按鈕 */
.edit-status-btn {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 4px 8px;
    margin-left: 8px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    color: #666;
}

.edit-status-btn:hover {
    background: #fff;
    border-color: #007bff;
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.status-details {
    font-size: 14px;
    line-height: 1.4;
}

.submitted-subjects, .pending-subjects {
    margin-bottom: 8px;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 5px;
}

.status-label {
    font-weight: bold;
    min-width: 60px;
    flex-shrink: 0;
}

.subjects-list {
    background: rgba(255, 255, 255, 0.6);
    padding: 2px 8px;
    border-radius: 8px;
    font-weight: normal;
}

.complete-badge {
    text-align: center;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #8B4513;
    padding: 5px 10px;
    border-radius: 12px;
    font-weight: bold;
    margin-top: 8px;
    animation: completePulse 2s infinite;
}

@keyframes completePulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* 舊版狀態項目樣式保留給歷史記錄使用 */
.status-item {
    padding: 15px;
    border-radius: 15px;
    text-align: center;
    font-weight: bold;
    font-size: 16px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.status-item.submitted {
    background: linear-gradient(45deg, #98FB98, #90EE90);
    color: #006400;
}

.status-item.pending {
    background: linear-gradient(45deg, #FFB6C1, #FFA0B4);
    color: #8B0000;
}

/* 學生卡片 */
.students-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 25px;
}

.student-card {
    background: linear-gradient(45deg, #F0F8FF, #E6E6FA);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border: 3px solid #DDA0DD;
    transition: transform 0.3s ease;
}

.student-card:hover {
    transform: translateY(-5px);
}

/* 學生標題區域 */
.student-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
}

.student-number-badge {
    background: linear-gradient(45deg, #FF69B4, #FF1493);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
    flex-shrink: 0;
}

.student-info {
    flex: 1;
}

.student-name {
    font-size: 22px;
    font-weight: bold;
    color: #8B008B;
    margin-bottom: 5px;
}

.student-seat-text {
    font-size: 14px;
    color: #666;
    background: rgba(255, 255, 255, 0.7);
    padding: 4px 10px;
    border-radius: 12px;
    display: inline-block;
    border: 1px solid #DDA0DD;
}

/* 原有的座號樣式保留作為備用 */
.student-number {
    font-size: 32px;
    font-weight: bold;
    color: #FF69B4;
    margin-bottom: 10px;
}

.student-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 15px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #32CD32;
}

.stat-label {
    font-size: 12px;
    color: #666;
}

.student-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.student-actions .btn-primary,
.student-actions .btn-danger {
    flex: 1;
    min-width: 80px;
    padding: 10px 15px;
    font-size: 14px;
}

/* Modal 樣式 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: linear-gradient(135deg, #F0F8FF 0%, #E6E6FA 100%);
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    border: 3px solid #FFB6C1;
    width: 90%;
    max-width: 500px;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px 15px;
    border-bottom: 2px solid #FFB6C1;
}

.modal-header h3 {
    color: #8B008B;
    font-size: 22px;
    margin: 0;
}

.modal-close {
    background: #FF6347;
    color: white;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    cursor: pointer;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #DC143C;
    transform: scale(1.1);
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    display: flex;
    gap: 15px;
    padding: 15px 25px 25px;
    justify-content: center;
}

/* 編輯繳交狀態模態框特定樣式 */
.student-info-display {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

.student-info-display p {
    margin: 5px 0;
    font-size: 16px;
}

.student-info-display strong {
    color: #495057;
    font-weight: 600;
}

.subject-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    max-height: 300px;
    overflow-y: auto;
}

.subject-checkbox-item {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    transition: all 0.2s ease;
}

.subject-checkbox-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    gap: 8px;
    margin: 0;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: #007bff;
}

.checkbox-text {
    font-size: 15px;
    font-weight: 500;
    color: #333;
}

.edit-submission-note {
    background: #e7f3ff;
    border: 1px solid #b8daff;
    border-radius: 8px;
    padding: 10px;
    margin-top: 15px;
}

.edit-submission-note p {
    margin: 0;
    text-align: center;
}

/* 提交時間顯示樣式 */
.subjects-with-time {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.subject-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 3px 0;
}

.subject-name {
    font-weight: 500;
}

.submission-time {
    font-size: 12px;
    color: #666;
    background: rgba(255, 255, 255, 0.8);
    padding: 2px 6px;
    border-radius: 8px;
    font-family: monospace;
}

.btn-secondary {
    background: linear-gradient(45deg, #778899, #696969);
    color: white;
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

/* 鼓勵訊息 */
.encouragement {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #8B4513;
    padding: 20px;
    border-radius: 20px;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    margin: 20px 0;
    box-shadow: 0 8px 20px rgba(255, 215, 0, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* 歷史記錄 */
.filter-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 3px solid #87CEEB;
}

/* 下載區域 */
.download-section {
    margin-top: 25px;
    padding-top: 25px;
    border-top: 2px solid #87CEEB;
}

.download-section h3 {
    color: #8B008B;
    margin-bottom: 15px;
    font-size: 20px;
    text-align: center;
}

.history-container {
    space-y: 20px;
}

.history-item {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 3px solid #DDA0DD;
}

.history-date {
    font-size: 22px;
    font-weight: bold;
    color: #8B008B;
    margin-bottom: 15px;
    text-align: center;
}

.history-subjects {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
    justify-content: center;
}

.history-subject {
    background: linear-gradient(45deg, #FFE4E1, #F0F8FF);
    padding: 8px 15px;
    border-radius: 15px;
    font-weight: bold;
    color: #8B008B;
    border: 2px solid #FFB6C1;
}

/* 通知系統 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, #32CD32, #228B22);
    color: white;
    padding: 15px 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    display: none;
    align-items: center;
    gap: 15px;
    font-weight: bold;
    font-size: 16px;
    z-index: 1000;
    max-width: 400px;
    animation: slideIn 0.5s ease;
}

.notification.error {
    background: linear-gradient(45deg, #FF6347, #DC143C);
}

.notification.show {
    display: flex;
}

.notification-close {
    background: rgba(255, 255, 255, 0.3);
    border: none;
    color: white;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    cursor: pointer;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* 搜尋區域 */
.search-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 3px solid #87CEEB;
}

.add-student-form {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 3px solid #98FB98;
}

.add-student-form h2 {
    color: #8B008B;
    text-align: center;
    margin-bottom: 20px;
    font-size: 24px;
}

/* 批次操作區域 */
.batch-operations-section {
    background: linear-gradient(135deg, #E6F3FF 0%, #CCE7FF 100%);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.1);
    border: 3px solid #007BFF;
}

.batch-operations-section h2 {
    color: #007BFF;
    text-align: center;
    margin-bottom: 20px;
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.batch-controls {
    margin-bottom: 15px;
}

.batch-controls .form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.batch-controls .form-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.selection-info {
    text-align: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    font-weight: bold;
    color: #007BFF;
}

/* 學生卡片批次選擇樣式 */
.student-card {
    position: relative;
    border: 3px solid transparent;
    transition: all 0.3s ease;
}

.student-card.selected {
    border-color: #007BFF;
    background: linear-gradient(135deg, #E6F3FF 0%, #CCE7FF 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

.student-card .student-checkbox {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 24px;
    height: 24px;
    cursor: pointer;
    z-index: 10;
    accent-color: #007BFF;
}

.student-card .student-header {
    padding-right: 35px; /* 為checkbox留出空間 */
}

/* 批次編輯模態框樣式 */
.batch-edit-info {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

.batch-edit-info p {
    margin: 5px 0;
    font-size: 16px;
}

.batch-edit-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.batch-edit-options .form-group {
    background: white;
    padding: 15px;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.batch-edit-options .form-group:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.batch-edit-options label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #495057;
}

.batch-edit-options input {
    width: 100%;
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.2s ease;
}

.batch-edit-options input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* 批次預覽區域 */
.batch-preview-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.batch-preview-section h4 {
    color: #495057;
    margin-bottom: 15px;
    text-align: center;
}

.batch-preview-container {
    max-height: 300px;
    overflow-y: auto;
    background: white;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 15px;
}

.batch-preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.batch-preview-item:hover {
    background-color: #f8f9fa;
}

.batch-preview-item:last-child {
    border-bottom: none;
}

.preview-original {
    font-weight: bold;
    color: #495057;
}

.preview-arrow {
    color: #007bff;
    margin: 0 15px;
    font-size: 18px;
}

.preview-new {
    font-weight: bold;
    color: #28a745;
}

.preview-changed {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    border-radius: 5px;
    padding: 2px 6px;
}

.preview-unchanged {
    color: #6c757d;
    font-style: italic;
}

/* 批次操作按鈕狀態 */
.btn-outline:disabled {
    background: #e9ecef;
    color: #6c757d;
    border-color: #dee2e6;
    cursor: not-allowed;
    opacity: 0.6;
}

.btn-primary:disabled, .btn-danger:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 響應式設計 - 批次操作區域 */
@media (max-width: 768px) {
    .batch-controls .form-row {
        flex-direction: column;
        gap: 10px;
    }
    
    .batch-controls .form-group {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .batch-edit-options {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .student-card .student-checkbox {
        width: 20px;
        height: 20px;
    }
    
    .student-card .student-header {
        padding-right: 30px;
    }
}

@media (max-width: 480px) {
    .batch-operations-section {
        padding: 15px;
    }
    
    .batch-controls .form-group {
        flex-direction: column;
        gap: 5px;
    }
    
    .batch-preview-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .preview-arrow {
        align-self: center;
        margin: 5px 0;
    }
}

/* 響應式設計 */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        margin: 5px;
        border-radius: 15px;
    }
    
    .sidebar {
        width: 100%;
        padding: 15px;
        border-right: none;
        border-bottom: 3px solid #FF69B4;
    }
    
    .nav-btn {
        padding: 12px 15px;
        font-size: 16px;
    }
    
    .nav-btn .text {
        display: none;
    }
    
    .nav-menu {
        display: flex;
        justify-content: space-around;
        margin-bottom: 15px;
    }
    
    .nav-item {
        margin-bottom: 0;
        flex: 1;
        margin-right: 10px;
    }
    
    .nav-item:last-child {
        margin-right: 0;
    }
    
    .leaderboard {
        display: none;
    }
    
    .main-content {
        padding: 15px;
    }
    
    .section-header h1 {
        font-size: 24px;
    }
    
    .form-row {
        flex-direction: column;
    }
    
    .homework-checklist {
        grid-template-columns: 1fr;
    }
    
    .status-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    
    .status-item-detailed {
        padding: 12px;
    }
    
    .status-header {
        font-size: 14px;
        margin-bottom: 8px;
    }
    
    .status-details {
        font-size: 13px;
    }
    
    .submitted-subjects, .pending-subjects {
        flex-direction: column;
        gap: 2px;
    }
    
    .status-label {
        min-width: auto;
    }
    
    .students-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    body {
        font-size: 14px;
    }
    
    .container {
        margin: 0;
        border-radius: 0;
    }
    
    .sidebar {
        padding: 10px;
        border-radius: 0;
    }
    
    .main-content {
        padding: 10px;
    }
    
    .section-header {
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .section-header h1 {
        font-size: 20px;
    }
    
    .teacher-section, .student-section, .status-section {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .form-group input, .form-group select {
        padding: 12px;
        font-size: 16px;
    }
    
    .btn-primary, .btn-success, .btn-danger {
        padding: 12px 20px;
        font-size: 16px;
    }
    
    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .status-item-detailed {
        padding: 10px;
    }
    
    .status-header {
        font-size: 13px;
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .status-count {
        align-self: flex-end;
    }
    
    .student-header {
        gap: 10px;
    }
    
    .student-number-badge {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .student-name {
        font-size: 18px;
    }
    
    .student-seat-text {
        font-size: 12px;
        padding: 3px 8px;
    }
}

/* 平板橫向 */
@media (min-width: 769px) and (max-width: 1024px) {
    .sidebar {
        width: 220px;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .homework-checklist {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .students-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
}

/* 觸控友善的調整 */
@media (hover: none) and (pointer: coarse) {
    .nav-btn, .btn-primary, .btn-success, .btn-danger {
        min-height: 44px;
        min-width: 44px;
    }
    
    .form-group input, .form-group select {
        min-height: 44px;
    }
    
    .homework-item input[type="checkbox"] {
        width: 30px;
        height: 30px;
    }
    
    .subject-remove {
        width: 30px;
        height: 30px;
        font-size: 16px;
    }
}

/* ===== 加分系統樣式 ===== */

/* 加分設定區域 */
.bonus-settings-section, .bonus-award-section, .bonus-summary-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.bonus-settings-section {
    border: 3px solid #FFD700;
}

.bonus-award-section {
    border: 3px solid #FF69B4;
}

.bonus-summary-section {
    border: 3px solid #32CD32;
}

.bonus-download-section {
    border: 3px solid #87CEEB;
}

.bonus-settings-section h2, .bonus-award-section h2, .bonus-summary-section h2, .bonus-download-section h2 {
    color: #8B008B;
    text-align: center;
    margin-bottom: 20px;
    font-size: 24px;
}

/* 加分學生網格 */
.bonus-students-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.bonus-student-card {
    background: linear-gradient(45deg, #F0F8FF, #E6E6FA);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border: 3px solid;
    transition: transform 0.3s ease;
}

.bonus-student-card:hover {
    transform: translateY(-3px);
}

.bonus-student-card.complete {
    border-color: #32CD32;
    background: linear-gradient(45deg, #F0FFF0, #E6FFE6);
}

.bonus-student-card.partial {
    border-color: #FFD700;
    background: linear-gradient(45deg, #FFFACD, #FFF8DC);
}

.bonus-student-card.pending {
    border-color: #FF69B4;
    background: linear-gradient(45deg, #FFE4E1, #FFF0F5);
}

/* 加分學生卡片標題 */
.bonus-student-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.student-number-small {
    background: linear-gradient(45deg, #FF69B4, #FF1493);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    box-shadow: 0 3px 10px rgba(255, 105, 180, 0.4);
}

.student-name-small {
    font-size: 18px;
    font-weight: bold;
    color: #8B008B;
    flex: 1;
    text-align: center;
}

.bonus-status {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: bold;
}

.bonus-status.awarded {
    background: linear-gradient(45deg, #32CD32, #228B22);
    color: white;
}

.bonus-status.not-awarded {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #8B4513;
}

/* 加分詳細資訊 */
.bonus-details {
    margin-bottom: 15px;
    font-size: 14px;
    line-height: 1.5;
}

.submission-info {
    font-weight: bold;
    color: #8B008B;
    margin-bottom: 8px;
    text-align: center;
}

.submitted-list, .pending-list {
    margin-bottom: 5px;
    padding: 5px 10px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.7);
}

.submitted-list {
    color: #006400;
}

.pending-list {
    color: #8B0000;
}

/* 加分顯示 */
.bonus-points-display {
    text-align: center;
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    border: 2px solid #FFD700;
}

.points-label {
    font-size: 14px;
    color: #666;
}

.points-value {
    font-size: 24px;
    font-weight: bold;
    color: #FF69B4;
    margin-left: 5px;
}

.awarded-points {
    display: block;
    font-size: 12px;
    color: #32CD32;
    margin-top: 5px;
}

/* 加分操作按鈕 */
.bonus-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.btn-award, .btn-remove {
    padding: 10px 15px;
    border: none;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.btn-award {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #8B4513;
}

.btn-award:hover:not(.disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(255, 215, 0, 0.3);
}

.btn-award.disabled {
    background: #ccc;
    color: #666;
    cursor: not-allowed;
}

.btn-remove {
    background: linear-gradient(45deg, #FF6347, #DC143C);
    color: white;
}

.btn-remove:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(255, 99, 71, 0.3);
}

/* 全域加分操作區域 */
.bonus-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* 加分統計網格 */
.bonus-summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.bonus-summary-card {
    background: linear-gradient(45deg, #F0F8FF, #E6E6FA);
    border-radius: 20px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border: 3px solid #DDA0DD;
    transition: transform 0.3s ease;
}

.bonus-summary-card:hover {
    transform: translateY(-3px);
}

.bonus-summary-card.rank-1 {
    border-color: #FFD700;
    background: linear-gradient(45deg, #FFFACD, #FFF8DC);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

.bonus-summary-card.rank-2 {
    border-color: #C0C0C0;
    background: linear-gradient(45deg, #F8F8FF, #F0F0F0);
}

.bonus-summary-card.rank-3 {
    border-color: #CD7F32;
    background: linear-gradient(45deg, #FFEFD5, #FFE4B5);
}

.summary-rank {
    font-size: 18px;
    font-weight: bold;
    color: #8B008B;
    margin-bottom: 10px;
}

.summary-student {
    margin-bottom: 15px;
}

.summary-seat {
    background: linear-gradient(45deg, #FF69B4, #FF1493);
    color: white;
    padding: 4px 8px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: bold;
    margin-right: 8px;
}

.summary-name {
    font-size: 16px;
    font-weight: bold;
    color: #8B008B;
}

.summary-points {
    margin-bottom: 10px;
}

.points-big {
    font-size: 36px;
    font-weight: bold;
    color: #FF69B4;
}

.points-unit {
    font-size: 18px;
    color: #666;
    margin-left: 3px;
}

.summary-days {
    font-size: 14px;
    color: #666;
    background: rgba(255, 255, 255, 0.7);
    padding: 5px 10px;
    border-radius: 15px;
    display: inline-block;
}

/* 作業追蹤頁面樣式 */
/* 追蹤篩選區域 */
.tracking-filter-section {
    background: linear-gradient(135deg, #FFE5CC 0%, #FFF0F5 100%);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 25px;
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.3);
    border: 2px solid #FFB6C1;
}

.tracking-filter-section h2 {
    color: #8B008B;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    font-size: 20px;
}

/* 追蹤摘要卡片 */
.tracking-summary-section {
    margin-bottom: 30px;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.summary-card {
    background: linear-gradient(135deg, #FFF0F5 0%, #FFE4E1 100%);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.2);
    border: 2px solid #FFB6C1;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(255, 182, 193, 0.3);
}

.summary-card.incomplete {
    background: linear-gradient(135deg, #FFE4E1 0%, #FFCCCB 100%);
    border-color: #FF6B6B;
}

.summary-card.completion {
    background: linear-gradient(135deg, #E8F5E8 0%, #D4FFCE 100%);
    border-color: #4CAF50;
}

.summary-icon {
    font-size: 48px;
    margin-right: 20px;
    opacity: 0.8;
}

.summary-content {
    flex: 1;
}

.summary-number {
    font-size: 32px;
    font-weight: bold;
    color: #8B008B;
    line-height: 1;
    margin-bottom: 5px;
}

.summary-card.incomplete .summary-number {
    color: #FF6B6B;
}

.summary-card.completion .summary-number {
    color: #4CAF50;
}

.summary-label {
    font-size: 14px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* 追蹤表格區域 */
.tracking-table-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 2px solid #FFB6C1;
}

.tracking-controls {
    margin-bottom: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #FFF8DC 0%, #FFFACD 100%);
    border-radius: 10px;
    border: 1px solid #DDD;
}

.tracking-table-container {
    overflow-x: auto;
    border-radius: 10px;
    border: 1px solid #DDD;
}

.tracking-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    font-size: 14px;
}

.tracking-table th {
    background: linear-gradient(135deg, #FF69B4 0%, #FF1493 100%);
    color: white;
    padding: 15px 10px;
    text-align: left;
    font-weight: bold;
    border-bottom: 2px solid #FF1493;
}

.tracking-table td {
    padding: 12px 10px;
    border-bottom: 1px solid #EEE;
    vertical-align: middle;
}

.tracking-table tbody tr:hover {
    background: linear-gradient(135deg, #FFF0F5 0%, #FFE4E1 100%);
}

.tracking-table tbody tr:nth-child(even) {
    background: #FAFAFA;
}

/* 座號標籤 */
.seat-badge {
    background: linear-gradient(45deg, #FF69B4, #FF1493);
    color: white;
    padding: 4px 8px;
    border-radius: 50%;
    font-weight: bold;
    font-size: 12px;
    display: inline-block;
    min-width: 24px;
    text-align: center;
}

/* 狀態標籤 */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
    display: inline-block;
}

.status-badge.completed {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

.status-badge.incomplete {
    background: linear-gradient(45deg, #FF6B6B, #FF5252);
    color: white;
}

/* 完成按鈕 */
.complete-btn {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.complete-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.complete-btn:disabled {
    background: #CCC;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 時間顯示 */
.timestamp {
    font-size: 12px;
    color: #666;
    font-style: italic;
}

/* 空狀態 */
.tracking-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state-content {
    max-width: 400px;
    margin: 0 auto;
}

.empty-state-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state-content h3 {
    font-size: 24px;
    margin-bottom: 10px;
    color: #8B008B;
}

.empty-state-content p {
    font-size: 16px;
    line-height: 1.5;
}

/* 批量操作區域 */
.batch-operations-section {
    background: linear-gradient(135deg, #E6F3FF 0%, #CCE7FF 100%);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 25px;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.1);
    border: 2px solid #007BFF;
}

.batch-operations-section h2 {
    color: #007BFF;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    font-size: 20px;
}

.batch-actions .form-row {
    gap: 15px;
}

/* 快速操作區域 */
.quick-actions-section {
    margin-bottom: 25px;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.quick-action-card {
    background: linear-gradient(135deg, #FFF8DC 0%, #FFFACD 100%);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
    border: 2px solid #FFD700;
    transition: all 0.3s ease;
}

.quick-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

.quick-action-icon {
    font-size: 48px;
    margin-bottom: 15px;
    text-align: center;
}

.quick-action-content h3 {
    font-size: 18px;
    color: #8B008B;
    margin-bottom: 10px;
    font-weight: bold;
}

.quick-action-content p {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    line-height: 1.4;
}

.btn-outline {
    background: transparent;
    color: #8B008B;
    border: 2px solid #8B008B;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-outline:hover {
    background: #8B008B;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(139, 0, 139, 0.3);
}

/* 響應式設計 - 作業追蹤頁面 */
@media (max-width: 768px) {
    .summary-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .summary-card {
        padding: 15px;
    }
    
    .summary-icon {
        font-size: 36px;
        margin-right: 15px;
    }
    
    .summary-number {
        font-size: 24px;
    }
    
    .tracking-controls .form-row {
        flex-direction: column;
        gap: 15px;
    }
    
    .tracking-table {
        font-size: 12px;
    }
    
    .tracking-table th,
    .tracking-table td {
        padding: 8px 6px;
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .quick-action-card {
        padding: 20px;
    }
    
    .batch-actions .form-row {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    .tracking-table th,
    .tracking-table td {
        padding: 6px 4px;
        font-size: 11px;
    }
    
    .complete-btn {
        padding: 6px 12px;
        font-size: 11px;
    }
    
    .tracking-filter-section,
    .batch-operations-section,
    .tracking-table-section {
        padding: 15px;
    }
    
    /* 響應式設計 - 重置按鈕 */
    .btn-reset-subtle {
        top: 10px;
        right: 10px;
        padding: 6px 12px;
        font-size: 12px;
        border-radius: 15px;
    }
}