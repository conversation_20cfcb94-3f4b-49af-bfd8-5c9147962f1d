// 全域變數
let currentStudents = [];
let currentSubjects = [];
let currentHomework = {};
let homeworkHistory = [];
let bonusPoints = {}; // 加分記錄 { "2024-01-15": { "1": { points: 15, subjects: ["國語", "數學", "英語"] } } }
let selectedStudents = new Set(); // 批次選擇的學生座號集合

// 鼓勵訊息陣列
const encouragementMessages = [
    "🎉 太棒了！繼續保持好習慣！",
    "⭐ 你是班上的小明星！",
    "🌟 今天也要加油喔！",
    "🎈 做得很好，老師為你驕傲！",
    "🦄 你就像獨角獸一樣特別！",
    "🌈 每一天都有進步，真棒！",
    "🎪 學習就像馬戲團一樣有趣！",
    "🚀 你的努力會帶你飛向星空！",
    "🏆 你是我們的小冠軍！",
    "🎁 努力學習是給自己最好的禮物！"
];

// DOM 元素
const navItems = document.querySelectorAll('.nav-item');
const contentSections = document.querySelectorAll('.content-section');
const currentDateElement = document.getElementById('current-date');
const subjectInput = document.getElementById('subject-input');
const addSubjectBtn = document.getElementById('add-subject-btn');
const resetSystemBtn = document.getElementById('reset-system-btn');
const subjectsList = document.getElementById('subjects-list');
const seatSelect = document.getElementById('seat-select');
const homeworkChecklist = document.getElementById('homework-checklist');
const submitHomeworkBtn = document.getElementById('submit-homework-btn');
const statusGrid = document.getElementById('status-grid');
const encouragementElement = document.getElementById('encouragement-message');

// 學生管理相關元素
const newSeatNumber = document.getElementById('new-seat-number');
const newStudentName = document.getElementById('new-student-name');
const addStudentBtn = document.getElementById('add-student-btn');
const studentSearch = document.getElementById('student-search');
const studentsGrid = document.getElementById('students-grid');

// 批次操作相關元素
const selectAllStudentsBtn = document.getElementById('select-all-students-btn');
const deselectAllStudentsBtn = document.getElementById('deselect-all-students-btn');
const batchEditStudentsBtn = document.getElementById('batch-edit-students-btn');
const batchDeleteStudentsBtn = document.getElementById('batch-delete-students-btn');
const selectedCount = document.getElementById('selected-count');

// 歷史記錄相關元素
const historyDate = document.getElementById('history-date');
const filterHistoryBtn = document.getElementById('filter-history-btn');
const historyContainer = document.getElementById('history-container');

// 下載功能相關元素
const downloadFormat = document.getElementById('download-format');
const downloadSingleBtn = document.getElementById('download-single-btn');
const downloadAllBtn = document.getElementById('download-all-btn');

// 編輯學生相關元素
const editStudentModal = document.getElementById('edit-student-modal');
const closeEditModal = document.getElementById('close-edit-modal');
const editSeatNumber = document.getElementById('edit-seat-number');
const editStudentName = document.getElementById('edit-student-name');
const saveStudentBtn = document.getElementById('save-student-btn');
const cancelEditBtn = document.getElementById('cancel-edit-btn');

// 編輯繳交狀態相關元素
const editSubmissionModal = document.getElementById('edit-submission-modal');
const closeSubmissionModal = document.getElementById('close-submission-modal');
const editSubmissionStudentName = document.getElementById('edit-submission-student-name');
const editSubmissionDate = document.getElementById('edit-submission-date');
const editSubmissionSubjects = document.getElementById('edit-submission-subjects');
const saveSubmissionBtn = document.getElementById('save-submission-btn');
const cancelSubmissionBtn = document.getElementById('cancel-submission-btn');

// 批次編輯模態框相關元素
const batchEditStudentsModal = document.getElementById('batch-edit-students-modal');
const closeBatchEditModal = document.getElementById('close-batch-edit-modal');
const batchEditCount = document.getElementById('batch-edit-count');
const batchSeatStart = document.getElementById('batch-seat-start');
const batchSeatEnd = document.getElementById('batch-seat-end');
const batchNamePrefix = document.getElementById('batch-name-prefix');
const batchNameSuffix = document.getElementById('batch-name-suffix');
const batchSeatIncrement = document.getElementById('batch-seat-increment');
const previewBatchChangesBtn = document.getElementById('preview-batch-changes-btn');
const saveBatchChangesBtn = document.getElementById('save-batch-changes-btn');
const cancelBatchEditBtn = document.getElementById('cancel-batch-edit-btn');
const batchPreviewContainer = document.getElementById('batch-preview-container');

// 目前編輯的學生資料
let currentEditingStudent = null;
let currentEditingSubmission = null;

// 排行榜和通知元素
const leaderboardList = document.getElementById('leaderboard-list');
const notification = document.getElementById('notification');

// 加分系統相關元素
const bonusDate = document.getElementById('bonus-date');
const bonusPointsValue = document.getElementById('bonus-points-value');
const loadBonusDateBtn = document.getElementById('load-bonus-date-btn');
const bonusStudentsGrid = document.getElementById('bonus-students-grid');
const awardAllBtn = document.getElementById('award-all-btn');
const clearBonusBtn = document.getElementById('clear-bonus-btn');
const bonusSummaryGrid = document.getElementById('bonus-summary-grid');

// 加分下載功能相關元素
const bonusDownloadFormat = document.getElementById('bonus-download-format');
const downloadSingleBonusBtn = document.getElementById('download-single-bonus-btn');
const downloadAllBonusBtn = document.getElementById('download-all-bonus-btn');
const downloadBonusSummaryBtn = document.getElementById('download-bonus-summary-btn');

// 初始化應用程式
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化函數
function initializeApp() {
    loadFromLocalStorage();
    setupEventListeners();
    updateCurrentDate();
    renderStudentOptions();
    renderSubjects();
    renderStudentsGrid();
    renderStatusGrid();
    renderLeaderboard();
    showRandomEncouragement();
    
    // 設定預設歷史查詢日期為今天
    historyDate.value = new Date().toISOString().split('T')[0];
    // 設定預設加分日期為今天
    bonusDate.value = new Date().toISOString().split('T')[0];
    
    // 初始化作業追蹤系統
    initializeHomeworkTracking();
}

// 設定事件監聽器
function setupEventListeners() {
    // 導航切換
    navItems.forEach(item => {
        item.addEventListener('click', () => switchSection(item.dataset.section));
    });

    // 科目管理
    addSubjectBtn.addEventListener('click', addSubject);
    resetSystemBtn.addEventListener('click', handleSystemReset);
    subjectInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') addSubject();
    });

    // 學生作業提交
    seatSelect.addEventListener('change', updateHomeworkChecklist);
    submitHomeworkBtn.addEventListener('click', submitHomework);

    // 學生管理
    addStudentBtn.addEventListener('click', addStudent);
    newStudentName.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') addStudent();
    });
    newSeatNumber.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') addStudent();
    });
    studentSearch.addEventListener('input', filterStudents);

    // 批次操作
    selectAllStudentsBtn.addEventListener('click', selectAllStudents);
    deselectAllStudentsBtn.addEventListener('click', deselectAllStudents);
    batchEditStudentsBtn.addEventListener('click', openBatchEditModal);
    batchDeleteStudentsBtn.addEventListener('click', batchDeleteStudents);

    // 歷史記錄
    filterHistoryBtn.addEventListener('click', filterHistory);

    // 下載功能
    downloadSingleBtn.addEventListener('click', downloadSingleRecord);
    downloadAllBtn.addEventListener('click', downloadAllRecords);

    // 編輯學生功能
    closeEditModal.addEventListener('click', closeEditStudentModal);
    cancelEditBtn.addEventListener('click', closeEditStudentModal);
    saveStudentBtn.addEventListener('click', saveStudentChanges);
    
    // 點擊 modal 背景關閉
    editStudentModal.addEventListener('click', (e) => {
        if (e.target === editStudentModal) {
            closeEditStudentModal();
        }
    });

    // 加分系統
    loadBonusDateBtn.addEventListener('click', loadBonusDate);
    awardAllBtn.addEventListener('click', awardAllBonus);
    clearBonusBtn.addEventListener('click', clearBonusPoints);

    // 加分下載功能
    downloadSingleBonusBtn.addEventListener('click', downloadSingleBonusRecord);
    downloadAllBonusBtn.addEventListener('click', downloadAllBonusRecords);
    downloadBonusSummaryBtn.addEventListener('click', downloadBonusSummaryReport);

    // 編輯繳交狀態相關事件
    closeSubmissionModal.addEventListener('click', closeEditSubmissionModal);
    cancelSubmissionBtn.addEventListener('click', closeEditSubmissionModal);
    saveSubmissionBtn.addEventListener('click', saveEditedSubmission);
    
    // 點擊模態框外部關閉
    editSubmissionModal.addEventListener('click', function(e) {
        if (e.target === editSubmissionModal) {
            closeEditSubmissionModal();
        }
    });

    // 批次編輯模態框事件
    closeBatchEditModal.addEventListener('click', closeBatchEditModal);
    cancelBatchEditBtn.addEventListener('click', closeBatchEditModal);
    previewBatchChangesBtn.addEventListener('click', previewBatchChanges);
    saveBatchChangesBtn.addEventListener('click', saveBatchChanges);
    
    // 點擊批次編輯模態框外部關閉
    batchEditStudentsModal.addEventListener('click', function(e) {
        if (e.target === batchEditStudentsModal) {
            closeBatchEditModal();
        }
    });

    // 批次編輯輸入框變化時啟用預覽按鈕
    [batchSeatStart, batchSeatEnd, batchNamePrefix, batchNameSuffix, batchSeatIncrement].forEach(input => {
        input.addEventListener('input', function() {
            previewBatchChangesBtn.disabled = false;
        });
    });

    // 通知系統
    document.querySelector('.notification-close').addEventListener('click', hideNotification);
}

// 從 localStorage 載入資料
function loadFromLocalStorage() {
    try {
        currentStudents = JSON.parse(localStorage.getItem('students') || '[]');
        currentSubjects = JSON.parse(localStorage.getItem('subjects') || '[]');
        currentHomework = JSON.parse(localStorage.getItem('currentHomework') || '{}');
        homeworkHistory = JSON.parse(localStorage.getItem('homeworkHistory') || '[]');
        bonusPoints = JSON.parse(localStorage.getItem('bonusPoints') || '{}');
        
        // 確保資料結構正確
        if (!Array.isArray(currentStudents)) currentStudents = [];
        if (!Array.isArray(currentSubjects)) currentSubjects = [];  
        if (!Array.isArray(homeworkHistory)) homeworkHistory = [];
        if (typeof bonusPoints !== 'object') bonusPoints = {};
        
        // 初始化今日作業如果不存在
        const today = new Date().toISOString().split('T')[0];
        if (!currentHomework[today]) {
            currentHomework[today] = { subjects: [], submissions: {} };
        }
    } catch (error) {
        console.error('載入資料時發生錯誤:', error);
        showNotification('載入資料時發生錯誤！', 'error');
    }
}

// 儲存到 localStorage
function saveToLocalStorage() {
    try {
        localStorage.setItem('students', JSON.stringify(currentStudents));
        localStorage.setItem('subjects', JSON.stringify(currentSubjects));
        localStorage.setItem('currentHomework', JSON.stringify(currentHomework));
        localStorage.setItem('homeworkHistory', JSON.stringify(homeworkHistory));
        localStorage.setItem('bonusPoints', JSON.stringify(bonusPoints));
    } catch (error) {
        console.error('儲存資料時發生錯誤:', error);
        showNotification('儲存資料時發生錯誤！', 'error');
    }
}

// 切換內容區塊
function switchSection(sectionId) {
    // 更新導航狀態
    navItems.forEach(item => {
        item.classList.toggle('active', item.dataset.section === sectionId);
    });

    // 切換內容區塊
    contentSections.forEach(section => {
        section.classList.toggle('active', section.id === sectionId);
    });

    // 根據切換的區塊執行相應的更新
    if (sectionId === 'student-list') {
        renderStudentsGrid();
    } else if (sectionId === 'homework-history') {
        filterHistory();
    } else if (sectionId === 'homework-entry') {
        renderStatusGrid();
        renderLeaderboard();
    } else if (sectionId === 'bonus-points') {
        renderBonusSummary();
    } else if (sectionId === 'homework-tracking') {
        loadTrackingData();
    }
}

// 更新當前日期顯示
function updateCurrentDate() {
    const today = new Date();
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric', 
        weekday: 'long' 
    };
    currentDateElement.textContent = today.toLocaleDateString('zh-TW', options);
}

// 新增科目
function addSubject() {
    const subject = subjectInput.value.trim();
    if (!subject) {
        showNotification('請輸入科目名稱！', 'error');
        return;
    }

    if (currentSubjects.includes(subject)) {
        showNotification('科目已存在！', 'error');
        return;
    }

    currentSubjects.push(subject);
    const today = new Date().toISOString().split('T')[0];
    if (!currentHomework[today]) {
        currentHomework[today] = { subjects: [], submissions: {} };
    }
    currentHomework[today].subjects.push(subject);

    subjectInput.value = '';
    renderSubjects();
    updateHomeworkChecklist();
    saveToLocalStorage();
    showNotification(`已新增科目：${subject}`, 'success');
}

// 移除科目
function removeSubject(subject) {
    const index = currentSubjects.indexOf(subject);
    if (index > -1) {
        currentSubjects.splice(index, 1);
        
        const today = new Date().toISOString().split('T')[0];
        if (currentHomework[today] && currentHomework[today].subjects) {
            const subjectIndex = currentHomework[today].subjects.indexOf(subject);
            if (subjectIndex > -1) {
                currentHomework[today].subjects.splice(subjectIndex, 1);
            }
        }

        renderSubjects();
        updateHomeworkChecklist();
        saveToLocalStorage();
        showNotification(`已移除科目：${subject}`, 'success');
    }
}

// 渲染科目列表
function renderSubjects() {
    const today = new Date().toISOString().split('T')[0];
    const todaySubjects = currentHomework[today]?.subjects || [];
    
    subjectsList.innerHTML = todaySubjects.map(subject => `
        <div class="subject-item">
            <span>${subject}</span>
            <button class="subject-remove" onclick="removeSubject('${subject}')">&times;</button>
        </div>
    `).join('');
}

// 新增學生
function addStudent() {
    const seatNumber = parseInt(newSeatNumber.value);
    const studentName = newStudentName.value.trim();

    if (!seatNumber || !studentName) {
        showNotification('請填寫完整的座號和姓名！', 'error');
        return;
    }

    if (seatNumber < 1 || seatNumber > 50) {
        showNotification('座號必須在 1-50 之間！', 'error');
        return;
    }

    // 檢查座號是否已存在
    if (currentStudents.find(student => student.seatNumber === seatNumber)) {
        showNotification('此座號已存在！', 'error');
        return;
    }

    const newStudent = {
        seatNumber: seatNumber,
        name: studentName,
        totalSubmissions: 0,
        totalHomework: 0
    };

    currentStudents.push(newStudent);
    currentStudents.sort((a, b) => a.seatNumber - b.seatNumber);

    newSeatNumber.value = '';
    newStudentName.value = '';
    
    renderStudentOptions();
    renderStudentsGrid();
    saveToLocalStorage();
    showNotification(`已新增學生：${seatNumber}號 ${studentName}`, 'success');
}

// 移除學生
function removeStudent(seatNumber) {
    if (confirm('確定要移除這位學生嗎？')) {
        const index = currentStudents.findIndex(student => student.seatNumber === seatNumber);
        if (index > -1) {
            const removedStudent = currentStudents.splice(index, 1)[0];
            renderStudentOptions();
            renderStudentsGrid();
            saveToLocalStorage();
            showNotification(`已移除學生：${removedStudent.seatNumber}號 ${removedStudent.name}`, 'success');
        }
    }
}

// 渲染學生選項
function renderStudentOptions() {
    const sortedStudents = [...currentStudents].sort((a, b) => a.seatNumber - b.seatNumber);
    seatSelect.innerHTML = '<option value="">請選擇座號</option>' + 
        sortedStudents.map(student => 
            `<option value="${student.seatNumber}">${student.seatNumber}號 - ${student.name}</option>`
        ).join('');
}

// 渲染學生網格
function renderStudentsGrid() {
    const searchTerm = studentSearch.value.toLowerCase();
    const filteredStudents = currentStudents.filter(student =>
        student.name.toLowerCase().includes(searchTerm) ||
        student.seatNumber.toString().includes(searchTerm)
    );

    studentsGrid.innerHTML = filteredStudents.map(student => {
        const completionRate = student.totalHomework > 0 ?
            Math.round((student.totalSubmissions / student.totalHomework) * 100) : 0;
        const isSelected = selectedStudents.has(student.seatNumber);
        
        return `
            <div class="student-card ${isSelected ? 'selected' : ''}" data-seat="${student.seatNumber}">
                <div class="student-selection">
                    <input type="checkbox"
                           class="student-checkbox"
                           data-seat="${student.seatNumber}"
                           ${isSelected ? 'checked' : ''}
                           onchange="toggleStudentSelection(${student.seatNumber})">
                </div>
                <div class="student-header">
                    <div class="student-number-badge">${student.seatNumber}</div>
                    <div class="student-info">
                        <div class="student-name">${student.name}</div>
                        <div class="student-seat-text">座號：${student.seatNumber}號</div>
                    </div>
                </div>
                <div class="student-stats">
                    <div class="stat-item">
                        <span class="stat-number">${student.totalSubmissions}</span>
                        <span class="stat-label">已繳交</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${completionRate}%</span>
                        <span class="stat-label">完成率</span>
                    </div>
                </div>
                <div class="student-actions">
                    <button class="btn-primary" onclick="editStudent(${student.seatNumber})">
                        ✏️ 修改
                    </button>
                    <button class="btn-danger" onclick="removeStudent(${student.seatNumber})">
                        🗑️ 移除
                    </button>
                </div>
            </div>
        `;
    }).join('');
    
    // 更新選擇計數
    updateSelectedCount();
}

// 篩選學生
function filterStudents() {
    renderStudentsGrid();
}

// 編輯學生資料
function editStudent(seatNumber) {
    const student = currentStudents.find(s => s.seatNumber === seatNumber);
    if (!student) {
        showNotification('找不到學生資料！', 'error');
        return;
    }
    
    currentEditingStudent = { ...student };
    editSeatNumber.value = student.seatNumber;
    editStudentName.value = student.name;
    
    editStudentModal.classList.add('show');
}

// 關閉編輯學生對話框
function closeEditStudentModal() {
    editStudentModal.classList.remove('show');
    currentEditingStudent = null;
    editSeatNumber.value = '';
    editStudentName.value = '';
}

// 儲存學生修改
function saveStudentChanges() {
    if (!currentEditingStudent) return;
    
    const newSeatNumber = parseInt(editSeatNumber.value);
    const newName = editStudentName.value.trim();
    
    if (!newSeatNumber || !newName) {
        showNotification('請填寫完整的座號和姓名！', 'error');
        return;
    }
    
    if (newSeatNumber < 1 || newSeatNumber > 50) {
        showNotification('座號必須在 1-50 之間！', 'error');
        return;
    }
    
    // 檢查新座號是否已被其他學生使用
    const existingStudent = currentStudents.find(s => 
        s.seatNumber === newSeatNumber && s.seatNumber !== currentEditingStudent.seatNumber
    );
    
    if (existingStudent) {
        showNotification('此座號已被其他學生使用！', 'error');
        return;
    }
    
    // 更新學生資料
    const studentIndex = currentStudents.findIndex(s => s.seatNumber === currentEditingStudent.seatNumber);
    if (studentIndex > -1) {
        // 更新作業記錄中的座號（如果座號有變更）
        if (currentEditingStudent.seatNumber !== newSeatNumber) {
            updateHomeworkRecordsForSeatChange(currentEditingStudent.seatNumber, newSeatNumber);
        }
        
        currentStudents[studentIndex] = {
            ...currentStudents[studentIndex],
            seatNumber: newSeatNumber,
            name: newName
        };
        
        // 重新排序學生列表
        currentStudents.sort((a, b) => a.seatNumber - b.seatNumber);
        
        renderStudentOptions();
        renderStudentsGrid();
        renderStatusGrid();
        saveToLocalStorage();
        
        showNotification(`學生資料已更新：${newSeatNumber}號 ${newName}`, 'success');
        closeEditStudentModal();
    }
}

// 更新作業記錄中的座號
function updateHomeworkRecordsForSeatChange(oldSeatNumber, newSeatNumber) {
    // 確保座號一致性，轉換為字串
    const oldSeatKey = String(oldSeatNumber);
    const newSeatKey = String(newSeatNumber);
    
    Object.values(currentHomework).forEach(dayData => {
        if (dayData.submissions && dayData.submissions[oldSeatKey]) {
            dayData.submissions[newSeatKey] = dayData.submissions[oldSeatKey];
            delete dayData.submissions[oldSeatKey];
        }
    });
}

// ===== 編輯繳交狀態功能 =====

// 開啟編輯繳交狀態模態框
function openEditSubmissionModal(seatKey, studentName, seatNumber) {
    const today = new Date().toISOString().split('T')[0];
    const todayData = currentHomework[today];
    
    if (!todayData || !todayData.subjects || todayData.subjects.length === 0) {
        showNotification('今日尚未新增任何科目，無法編輯繳交狀況！', 'error');
        return;
    }
    
    // 設定當前編輯的繳交資料
    currentEditingSubmission = {
        seatKey: seatKey,
        studentName: studentName,
        seatNumber: seatNumber,
        date: today
    };
    
    // 更新模態框顯示資訊
    editSubmissionStudentName.textContent = `${seatNumber}號 ${studentName}`;
    editSubmissionDate.textContent = new Date(today).toLocaleDateString('zh-TW', {
        year: 'numeric',
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
    });
    
    // 生成科目複選框
    const currentSubmissions = todayData.submissions[seatKey] || {};
    editSubmissionSubjects.innerHTML = todayData.subjects.map(subject => {
        const submission = currentSubmissions[subject];
        // 處理新的資料結構 (物件格式) 和舊的資料結構 (布林值格式)
        const isChecked = typeof submission === 'object' ? submission.completed : !!submission;
        
        return `
        <div class="subject-checkbox-item">
            <label class="checkbox-label">
                <input type="checkbox" 
                       id="edit-subject-${subject}" 
                       value="${subject}" 
                       ${isChecked ? 'checked' : ''}>
                <span class="checkbox-text">${subject}</span>
            </label>
        </div>
    `;
    }).join('');
    
    // 顯示模態框
    editSubmissionModal.style.display = 'flex';
}

// 關閉編輯繳交狀態模態框
function closeEditSubmissionModal() {
    editSubmissionModal.style.display = 'none';
    currentEditingSubmission = null;
}

// 儲存編輯後的繳交狀態
function saveEditedSubmission() {
    if (!currentEditingSubmission) {
        showNotification('找不到編輯資料！', 'error');
        return;
    }
    
    const { seatKey, studentName, date } = currentEditingSubmission;
    const todayData = currentHomework[date];
    
    if (!todayData) {
        showNotification('找不到該日期的作業資料！', 'error');
        return;
    }
    
    // 確保 submissions 物件存在
    if (!todayData.submissions) {
        todayData.submissions = {};
    }
    if (!todayData.submissions[seatKey]) {
        todayData.submissions[seatKey] = {};
    }
    
    // 收集所有複選框的狀態
    const checkboxes = editSubmissionSubjects.querySelectorAll('input[type="checkbox"]');
    const newSubmissions = {};
    const currentTime = new Date().toISOString();
    let changedCount = 0;
    
    checkboxes.forEach(checkbox => {
        const subject = checkbox.value;
        const isChecked = checkbox.checked;
        const existingSubmission = todayData.submissions[seatKey][subject];
        
        // 處理新的資料結構和舊的資料結構
        const wasChecked = typeof existingSubmission === 'object' 
            ? existingSubmission.completed 
            : !!existingSubmission;
        
        // 保留原有時間戳記或設置新的時間戳記
        let timestamp = null;
        if (isChecked) {
            if (typeof existingSubmission === 'object' && existingSubmission.timestamp) {
                // 如果之前已經有時間戳記且仍然是已完成狀態，保留原時間
                timestamp = wasChecked ? existingSubmission.timestamp : currentTime;
            } else {
                // 新的提交或從舊格式轉換，設置新時間
                timestamp = currentTime;
            }
        }
        
        newSubmissions[subject] = {
            completed: isChecked,
            timestamp: timestamp
        };
        
        if (isChecked !== wasChecked) {
            changedCount++;
        }
    });
    
    // 更新繳交狀態
    todayData.submissions[seatKey] = newSubmissions;
    
    // 更新學生統計
    updateStudentStats();
    
    // 儲存資料
    saveToLocalStorage();
    
    // 重新渲染相關元件
    renderStatusGrid();
    renderLeaderboard();
    
    // 顯示成功訊息
    if (changedCount > 0) {
        showNotification(`已更新 ${studentName} 的繳交狀況（${changedCount} 項變更）`, 'success');
    } else {
        showNotification(`${studentName} 的繳交狀況沒有變更`, 'info');
    }
    
    // 關閉模態框
    closeEditSubmissionModal();
}

// 更新作業清單
function updateHomeworkChecklist() {
    const selectedSeat = seatSelect.value;
    const today = new Date().toISOString().split('T')[0];
    const todaySubjects = currentHomework[today]?.subjects || [];

    if (!selectedSeat || todaySubjects.length === 0) {
        homeworkChecklist.innerHTML = todaySubjects.length === 0 ? 
            '<p style="text-align: center; color: #666; font-size: 18px;">今日尚未新增任何科目</p>' :
            '<p style="text-align: center; color: #666; font-size: 18px;">請先選擇座號</p>';
        submitHomeworkBtn.disabled = true;
        return;
    }

    // 確保座號一致性，轉換為字串
    const seatKey = String(selectedSeat);
    const currentSubmissions = currentHomework[today]?.submissions?.[seatKey] || {};

    homeworkChecklist.innerHTML = todaySubjects.map(subject => `
        <div class="homework-item">
            <label>
                <input type="checkbox" value="${subject}" 
                       ${currentSubmissions[subject] ? 'checked' : ''}>
                <span>${subject}</span>
            </label>
        </div>
    `).join('');

    submitHomeworkBtn.disabled = false;
}

// 提交作業
function submitHomework() {
    const selectedSeat = seatSelect.value;
    if (!selectedSeat) {
        showNotification('請選擇座號！', 'error');
        return;
    }

    const today = new Date().toISOString().split('T')[0];
    const currentTime = new Date().toISOString();
    const checkboxes = homeworkChecklist.querySelectorAll('input[type="checkbox"]');
    const submissions = {};

    // 為每個科目記錄提交狀態和時間戳記
    checkboxes.forEach(checkbox => {
        const subject = checkbox.value;
        const isChecked = checkbox.checked;
        
        submissions[subject] = {
            completed: isChecked,
            timestamp: isChecked ? currentTime : null // 只有已完成的作業才記錄時間
        };
    });

    // 確保 submissions 物件存在
    if (!currentHomework[today]) {
        currentHomework[today] = { subjects: [], submissions: {} };
    }
    if (!currentHomework[today].submissions) {
        currentHomework[today].submissions = {};
    }
    
    // 將座號轉換為字串以確保一致性
    const seatKey = String(selectedSeat);
    currentHomework[today].submissions[seatKey] = submissions;

    // 更新學生統計
    updateStudentStats();
    
    renderStatusGrid();
    renderLeaderboard();
    saveToLocalStorage();
    showNotification(`${seatKey}號學生作業提交成功！`, 'success');
    showRandomEncouragement();

    // 清空選擇
    seatSelect.value = '';
    homeworkChecklist.innerHTML = '<p style="text-align: center; color: #666; font-size: 18px;">請先選擇座號</p>';
    submitHomeworkBtn.disabled = true;
}

// 更新學生統計
function updateStudentStats() {
    currentStudents.forEach(student => {
        let totalSubmissions = 0;
        let totalHomework = 0;

        Object.values(currentHomework).forEach(dayData => {
            if (dayData.subjects && dayData.subjects.length > 0) {
                totalHomework += dayData.subjects.length;
                
                // 確保座號一致性，轉換為字串
                const seatKey = String(student.seatNumber);
                const studentSubmissions = dayData.submissions[seatKey] || {};
                // 處理新的資料結構 (物件格式) 和舊的資料結構 (布林值格式)
                totalSubmissions += Object.values(studentSubmissions).filter(submission => {
                    // 新格式：submission.completed，舊格式：submission 本身是布林值
                    return typeof submission === 'object' ? submission.completed : submission;
                }).length;
            }
        });

        student.totalSubmissions = totalSubmissions;
        student.totalHomework = totalHomework;
    });
}

// 格式化提交時間
function formatSubmissionTime(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    
    if (isToday) {
        // 今天的話只顯示時間
        return date.toLocaleTimeString('zh-TW', {
            hour: '2-digit',
            minute: '2-digit'
        });
    } else {
        // 其他日期顯示完整日期時間
        return date.toLocaleString('zh-TW', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

// 渲染狀態網格
function renderStatusGrid() {
    const today = new Date().toISOString().split('T')[0];
    const todayData = currentHomework[today];
    
    if (!todayData || !todayData.subjects || todayData.subjects.length === 0) {
        statusGrid.innerHTML = '<p style="text-align: center; color: #666; font-size: 18px; grid-column: 1 / -1;">今日尚未新增任何科目</p>';
        return;
    }

    const sortedStudents = [...currentStudents].sort((a, b) => a.seatNumber - b.seatNumber);
    
    statusGrid.innerHTML = sortedStudents.map(student => {
        // 確保座號一致性，轉換為字串
        const seatKey = String(student.seatNumber);
        const submissions = todayData.submissions[seatKey] || {};
        const submittedSubjects = [];
        const pendingSubjects = [];
        
        todayData.subjects.forEach(subject => {
            const submission = submissions[subject];
            // 處理新的資料結構 (物件格式) 和舊的資料結構 (布林值格式)
            const isCompleted = typeof submission === 'object' ? submission.completed : submission;
            
            if (isCompleted) {
                const timestamp = typeof submission === 'object' ? submission.timestamp : null;
                submittedSubjects.push({ subject, timestamp });
            } else {
                pendingSubjects.push(subject);
            }
        });
        
        const submittedCount = submittedSubjects.length;
        const totalCount = todayData.subjects.length;
        const hasSubmitted = submittedCount > 0;
        const isComplete = submittedCount === totalCount;
        
        // 決定狀態樣式
        let statusClass = 'pending';
        if (isComplete) {
            statusClass = 'completed';
        } else if (hasSubmitted) {
            statusClass = 'partial';
        }
        
        return `
            <div class="status-item-detailed ${statusClass}">
                <div class="status-header">
                    <span class="student-info">${student.seatNumber}號 ${student.name}</span>
                    <span class="status-count">${submittedCount}/${totalCount}</span>
                    <button class="edit-status-btn" onclick="openEditSubmissionModal('${seatKey}', '${student.name}', ${student.seatNumber})" title="編輯繳交狀況">
                        ✏️
                    </button>
                </div>
                <div class="status-details">
                    ${submittedSubjects.length > 0 ? `
                        <div class="submitted-subjects">
                            <span class="status-label">✅ 已交：</span>
                            <div class="subjects-with-time">
                                ${submittedSubjects.map(item => {
                                    const subject = typeof item === 'string' ? item : item.subject;
                                    const timestamp = typeof item === 'object' ? item.timestamp : null;
                                    const timeStr = formatSubmissionTime(timestamp);
                                    return `
                                        <div class="subject-item">
                                            <span class="subject-name">${subject}</span>
                                            ${timeStr ? `<span class="submission-time">${timeStr}</span>` : ''}
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    ` : ''}
                    ${pendingSubjects.length > 0 ? `
                        <div class="pending-subjects">
                            <span class="status-label">❌ 未交：</span>
                            <span class="subjects-list">${pendingSubjects.join('、')}</span>
                        </div>
                    ` : ''}
                    ${isComplete ? `
                        <div class="complete-badge">🎉 全部完成！</div>
                    ` : ''}
                </div>
            </div>
        `;
    }).join('');
}

// 渲染排行榜
function renderLeaderboard() {
    updateStudentStats();
    
    const leaderboard = [...currentStudents]
        .filter(student => student.totalHomework > 0)
        .sort((a, b) => {
            const aRate = a.totalSubmissions / a.totalHomework;
            const bRate = b.totalSubmissions / b.totalHomework;
            if (aRate !== bRate) return bRate - aRate;
            return b.totalSubmissions - a.totalSubmissions;
        })
        .slice(0, 5);

    leaderboardList.innerHTML = leaderboard.map((student, index) => {
        const completionRate = Math.round((student.totalSubmissions / student.totalHomework) * 100);
        const medals = ['🥇', '🥈', '🥉', '🏅', '⭐'];
        
        return `
            <div class="leaderboard-item">
                <span class="leaderboard-rank">${medals[index]} ${index + 1}.</span>
                <span>${student.seatNumber}號 ${student.name.length > 4 ? student.name.slice(0, 4) + '...' : student.name}</span>
                <span class="leaderboard-score">${completionRate}%</span>
            </div>
        `;
    }).join('');

    if (leaderboard.length === 0) {
        leaderboardList.innerHTML = '<p style="text-align: center; color: #666; font-size: 14px;">暫無排行資料</p>';
    }
}

// 顯示隨機鼓勵訊息
function showRandomEncouragement() {
    const randomMessage = encouragementMessages[Math.floor(Math.random() * encouragementMessages.length)];
    encouragementElement.innerHTML = `<p>${randomMessage}</p>`;
    
    // 3秒後淡出
    setTimeout(() => {
        encouragementElement.style.opacity = '0.7';
    }, 3000);
    
    setTimeout(() => {
        encouragementElement.style.opacity = '1';
    }, 6000);
}

// 篩選歷史記錄
function filterHistory() {
    const selectedDate = historyDate.value;
    if (!selectedDate) {
        historyContainer.innerHTML = '<p style="text-align: center; color: #666; font-size: 18px;">請選擇日期</p>';
        return;
    }

    const dateData = currentHomework[selectedDate];
    if (!dateData || !dateData.subjects || dateData.subjects.length === 0) {
        historyContainer.innerHTML = '<p style="text-align: center; color: #666; font-size: 18px;">該日期沒有作業記錄</p>';
        return;
    }

    const formattedDate = new Date(selectedDate).toLocaleDateString('zh-TW', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
    });

    const sortedStudents = [...currentStudents].sort((a, b) => a.seatNumber - b.seatNumber);
    
    historyContainer.innerHTML = `
        <div class="history-item">
            <div class="history-date">${formattedDate}</div>
            <div class="history-subjects">
                ${dateData.subjects.map(subject => `<span class="history-subject">${subject}</span>`).join('')}
            </div>
            <div class="status-grid">
                ${sortedStudents.map(student => {
                    const submissions = dateData.submissions[student.seatNumber] || {};
                    const submittedCount = Object.values(submissions).filter(Boolean).length;
                    const totalCount = dateData.subjects.length;
                    const hasSubmitted = submittedCount > 0;
                    
                    return `
                        <div class="status-item ${hasSubmitted ? 'submitted' : 'pending'}">
                            <div>${student.seatNumber}號 ${student.name}</div>
                            <div>${submittedCount}/${totalCount}</div>
                        </div>
                    `;
                }).join('')}
            </div>
        </div>
    `;
}

// 通知系統
function showNotification(message, type = 'success') {
    const notificationText = notification.querySelector('.notification-text');
    notificationText.textContent = message;
    
    notification.className = `notification ${type} show`;
    
    // 自動隱藏
    setTimeout(() => {
        hideNotification();
    }, 4000);
}

function hideNotification() {
    notification.classList.remove('show');
}

// 下載單日作業記錄
function downloadSingleRecord() {
    const selectedDate = historyDate.value;
    if (!selectedDate) {
        showNotification('請先選擇日期！', 'error');
        return;
    }

    const format = downloadFormat.value;
    const dateData = currentHomework[selectedDate];
    
    if (!dateData || !dateData.subjects || dateData.subjects.length === 0) {
        showNotification('該日期沒有作業記錄！', 'error');
        return;
    }

    const fileName = `作業記錄_${selectedDate}`;
    
    switch (format) {
        case 'csv':
            downloadAsCSV([{ date: selectedDate, data: dateData }], fileName);
            break;
        case 'excel':
            downloadAsExcel([{ date: selectedDate, data: dateData }], fileName);
            break;
        case 'json':
            downloadAsJSON({ [selectedDate]: dateData }, fileName);
            break;
    }
    
    showNotification(`已下載 ${selectedDate} 的作業記錄！`, 'success');
}

// 下載全部作業記錄
function downloadAllRecords() {
    if (Object.keys(currentHomework).length === 0) {
        showNotification('尚無作業記錄可下載！', 'error');
        return;
    }

    const format = downloadFormat.value;
    const fileName = `全部作業記錄_${new Date().toISOString().split('T')[0]}`;
    
    const allRecords = Object.entries(currentHomework)
        .filter(([date, data]) => data.subjects && data.subjects.length > 0)
        .map(([date, data]) => ({ date, data }))
        .sort((a, b) => new Date(b.date) - new Date(a.date));
    
    if (allRecords.length === 0) {
        showNotification('尚無有效的作業記錄可下載！', 'error');
        return;
    }
    
    switch (format) {
        case 'csv':
            downloadAsCSV(allRecords, fileName);
            break;
        case 'excel':
            downloadAsExcel(allRecords, fileName);
            break;
        case 'json':
            downloadAsJSON(currentHomework, fileName);
            break;
    }
    
    showNotification(`已下載全部作業記錄 (${allRecords.length} 天)！`, 'success');
}

// 下載為CSV格式
function downloadAsCSV(records, fileName) {
    let csvContent = '\uFEFF'; // UTF-8 BOM for Chinese characters
    csvContent += '日期,座號,學生姓名,科目,繳交狀態,繳交時間\n';
    
    records.forEach(({ date, data }) => {
        const formattedDate = new Date(date).toLocaleDateString('zh-TW');
        data.subjects.forEach(subject => {
            currentStudents.forEach(student => {
                // 確保座號一致性，轉換為字串
                const seatKey = String(student.seatNumber);
                const submissions = data.submissions[seatKey] || {};
                const submission = submissions[subject];
                
                // 處理新的資料結構 (物件格式) 和舊的資料結構 (布林值格式)
                let status = '未繳交';
                let submissionTime = '';
                
                if (submission) {
                    if (typeof submission === 'object') {
                        status = submission.completed ? '已繳交' : '未繳交';
                        if (submission.completed && submission.timestamp) {
                            submissionTime = new Date(submission.timestamp).toLocaleString('zh-TW', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit'
                            });
                        }
                    } else {
                        status = submission ? '已繳交' : '未繳交';
                    }
                }
                
                csvContent += `${formattedDate},${student.seatNumber},${student.name},${subject},${status},${submissionTime}\n`;
            });
        });
    });
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    downloadFile(blob, `${fileName}.csv`);
}

// 下載為Excel格式（實際上是CSV但可以被Excel開啟）
function downloadAsExcel(records, fileName) {
    let content = '\uFEFF'; // UTF-8 BOM
    
    // 建立摘要表
    content += '作業記錄摘要\n';
    content += '日期,總科目數,總學生數,總繳交率\n';
    
    records.forEach(({ date, data }) => {
        const formattedDate = new Date(date).toLocaleDateString('zh-TW');
        const totalSubjects = data.subjects.length;
        const totalStudents = currentStudents.length;
        const totalPossible = totalSubjects * totalStudents;
        
        let totalSubmitted = 0;
        currentStudents.forEach(student => {
            // 確保座號一致性，轉換為字串
            const seatKey = String(student.seatNumber);
            const submissions = data.submissions[seatKey] || {};
            // 處理新的資料結構和舊的資料結構
            totalSubmitted += Object.values(submissions).filter(submission => {
                return typeof submission === 'object' ? submission.completed : submission;
            }).length;
        });
        
        const completionRate = totalPossible > 0 ? Math.round((totalSubmitted / totalPossible) * 100) : 0;
        content += `${formattedDate},${totalSubjects},${totalStudents},${completionRate}%\n`;
    });
    
    content += '\n詳細記錄\n';
    content += '日期,座號,學生姓名,科目,繳交狀態,繳交時間\n';
    
    records.forEach(({ date, data }) => {
        const formattedDate = new Date(date).toLocaleDateString('zh-TW');
        data.subjects.forEach(subject => {
            currentStudents.forEach(student => {
                // 確保座號一致性，轉換為字串
                const seatKey = String(student.seatNumber);
                const submissions = data.submissions[seatKey] || {};
                const submission = submissions[subject];
                
                // 處理新的資料結構 (物件格式) 和舊的資料結構 (布林值格式)
                let status = '未繳交';
                let submissionTime = '';
                
                if (submission) {
                    if (typeof submission === 'object') {
                        status = submission.completed ? '已繳交' : '未繳交';
                        if (submission.completed && submission.timestamp) {
                            submissionTime = new Date(submission.timestamp).toLocaleString('zh-TW', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit'
                            });
                        }
                    } else {
                        status = submission ? '已繳交' : '未繳交';
                    }
                }
                
                content += `${formattedDate},${student.seatNumber},${student.name},${subject},${status},${submissionTime}\n`;
            });
        });
    });
    
    const blob = new Blob([content], { type: 'application/vnd.ms-excel;charset=utf-8;' });
    downloadFile(blob, `${fileName}.xls`);
}

// 下載為JSON格式
function downloadAsJSON(data, fileName) {
    const exportData = {
        exportDate: new Date().toISOString(),
        students: currentStudents,
        homeworkRecords: data,
        statistics: generateStatistics()
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json;charset=utf-8;' });
    downloadFile(blob, `${fileName}.json`);
}

// 生成統計資料
function generateStatistics() {
    updateStudentStats();
    
    const stats = {
        totalStudents: currentStudents.length,
        totalDays: Object.keys(currentHomework).filter(date => {
            const data = currentHomework[date];
            return data.subjects && data.subjects.length > 0;
        }).length,
        studentStats: currentStudents.map(student => ({
            seatNumber: student.seatNumber,
            name: student.name,
            totalSubmissions: student.totalSubmissions,
            totalHomework: student.totalHomework,
            completionRate: student.totalHomework > 0 ? Math.round((student.totalSubmissions / student.totalHomework) * 100) : 0
        })),
        subjectStats: {}
    };
    
    // 計算各科目統計
    Object.values(currentHomework).forEach(dayData => {
        if (dayData.subjects) {
            dayData.subjects.forEach(subject => {
                if (!stats.subjectStats[subject]) {
                    stats.subjectStats[subject] = { total: 0, submitted: 0 };
                }
                stats.subjectStats[subject].total += currentStudents.length;
                
                currentStudents.forEach(student => {
                    const submissions = dayData.submissions[student.seatNumber] || {};
                    if (submissions[subject]) {
                        stats.subjectStats[subject].submitted++;
                    }
                });
            });
        }
    });
    
    return stats;
}

// 通用下載檔案函數
function downloadFile(blob, fileName) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// 資料導出功能（額外功能）
function exportData() {
    const data = {
        students: currentStudents,
        subjects: currentSubjects,
        homework: currentHomework,
        exportDate: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `homework-data-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showNotification('資料導出成功！', 'success');
}

// 資料導入功能（額外功能）
function importData(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = JSON.parse(e.target.result);
            
            if (data.students && data.homework) {
                if (confirm('導入資料將覆蓋現有資料，確定要繼續嗎？')) {
                    currentStudents = data.students || [];
                    currentSubjects = data.subjects || [];
                    currentHomework = data.homework || {};
                    
                    saveToLocalStorage();
                    initializeApp();
                    showNotification('資料導入成功！', 'success');
                }
            } else {
                showNotification('資料格式不正確！', 'error');
            }
        } catch (error) {
            showNotification('檔案讀取失敗！', 'error');
        }
    };
    reader.readAsText(file);
}

// 清空所有資料（維護功能）
function clearAllData() {
    if (confirm('確定要清空所有資料嗎？此操作無法復原！')) {
        if (confirm('再次確認：這將刪除所有學生、科目和作業記錄！')) {
            localStorage.clear();
            currentStudents = [];
            currentSubjects = [];
            currentHomework = {};
            homeworkHistory = [];
            
            initializeApp();
            showNotification('所有資料已清空！', 'success');
        }
    }
}

// 新增控制台命令（開發者工具）
window.HomeworkSystem = {
    exportData,
    importData,
    clearAllData,
    students: () => currentStudents,
    subjects: () => currentSubjects,
    homework: () => currentHomework,
    stats: () => {
        updateStudentStats();
        return currentStudents.map(student => ({
            seat: student.seatNumber,
            name: student.name,
            submissions: student.totalSubmissions,
            total: student.totalHomework,
            rate: student.totalHomework > 0 ? Math.round((student.totalSubmissions / student.totalHomework) * 100) : 0
        }));
    }
};

// 鍵盤快捷鍵
document.addEventListener('keydown', function(e) {
    // Alt + 1: 切換到作業登記
    if (e.altKey && e.key === '1') {
        e.preventDefault();
        switchSection('homework-entry');
    }
    // Alt + 2: 切換到學生名單
    else if (e.altKey && e.key === '2') {
        e.preventDefault();
        switchSection('student-list');
    }
    // Alt + 3: 切換到作業記錄
    else if (e.altKey && e.key === '3') {
        e.preventDefault();
        switchSection('homework-history');
    }
    // Alt + 4: 切換到加分系統
    else if (e.altKey && e.key === '4') {
        e.preventDefault();
        switchSection('bonus-points');
    }
});

// 定期自動儲存（每3分鐘）
setInterval(saveToLocalStorage, 180000);

// 頁面離開前儲存資料
window.addEventListener('beforeunload', saveToLocalStorage);

// ===== 加分系統功能 =====

// 載入指定日期的作業資料
function loadBonusDate() {
    const selectedDate = bonusDate.value;
    if (!selectedDate) {
        showNotification('請選擇日期！', 'error');
        return;
    }

    const dateData = currentHomework[selectedDate];
    if (!dateData || !dateData.subjects || dateData.subjects.length === 0) {
        showNotification('該日期沒有作業記錄！', 'error');
        bonusStudentsGrid.innerHTML = '<p style="text-align: center; color: #666; font-size: 18px;">該日期沒有作業記錄</p>';
        return;
    }

    renderBonusStudents(selectedDate, dateData);
    showNotification(`已載入 ${selectedDate} 的作業資料`, 'success');
}

// 渲染加分學生網格
function renderBonusStudents(date, dateData) {
    const pointsPerHomework = parseInt(bonusPointsValue.value) || 5;
    const sortedStudents = [...currentStudents].sort((a, b) => a.seatNumber - b.seatNumber);
    
    bonusStudentsGrid.innerHTML = sortedStudents.map(student => {
        const seatKey = String(student.seatNumber);
        const submissions = dateData.submissions[seatKey] || {};
        const submittedSubjects = [];
        const pendingSubjects = [];
        
        dateData.subjects.forEach(subject => {
            if (submissions[subject]) {
                submittedSubjects.push(subject);
            } else {
                pendingSubjects.push(subject);
            }
        });
        
        const submittedCount = submittedSubjects.length;
        const totalCount = dateData.subjects.length;
        const potentialPoints = submittedCount * pointsPerHomework;
        const isComplete = submittedCount === totalCount;
        
        // 檢查是否已經加過分
        const currentBonus = bonusPoints[date]?.[seatKey];
        const hasBonus = currentBonus && currentBonus.points > 0;
        
        return `
            <div class="bonus-student-card ${isComplete ? 'complete' : submittedCount > 0 ? 'partial' : 'pending'}">
                <div class="bonus-student-header">
                    <div class="student-number-small">${student.seatNumber}</div>
                    <div class="student-name-small">${student.name}</div>
                    <div class="bonus-status ${hasBonus ? 'awarded' : 'not-awarded'}">
                        ${hasBonus ? '✅ 已加分' : '⭐ 待加分'}
                    </div>
                </div>
                <div class="bonus-details">
                    <div class="submission-info">
                        完成：${submittedCount}/${totalCount} 份作業
                    </div>
                    ${submittedSubjects.length > 0 ? `
                        <div class="submitted-list">
                            ✅ ${submittedSubjects.join('、')}
                        </div>
                    ` : ''}
                    ${pendingSubjects.length > 0 ? `
                        <div class="pending-list">
                            ❌ ${pendingSubjects.join('、')}
                        </div>
                    ` : ''}
                </div>
                <div class="bonus-points-display">
                    <span class="points-label">可得分數：</span>
                    <span class="points-value">${potentialPoints}分</span>
                    ${hasBonus ? `<span class="awarded-points">(已獲得 ${currentBonus.points}分)</span>` : ''}
                </div>
                <div class="bonus-actions">
                    <button class="btn-award ${hasBonus ? 'disabled' : ''}" 
                            onclick="awardIndividualBonus('${date}', ${student.seatNumber})"
                            ${hasBonus ? 'disabled' : ''}>
                        ${hasBonus ? '已加分' : '⭐ 加分'}
                    </button>
                    ${hasBonus ? `
                        <button class="btn-remove" onclick="removeIndividualBonus('${date}', ${student.seatNumber})">
                            🗑️ 移除
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }).join('');
}

// 個別學生加分
function awardIndividualBonus(date, seatNumber) {
    const dateData = currentHomework[date];
    if (!dateData) return;
    
    const pointsPerHomework = parseInt(bonusPointsValue.value) || 5;
    const seatKey = String(seatNumber);
    const submissions = dateData.submissions[seatKey] || {};
    const submittedSubjects = [];
    
    dateData.subjects.forEach(subject => {
        if (submissions[subject]) {
            submittedSubjects.push(subject);
        }
    });
    
    const points = submittedSubjects.length * pointsPerHomework;
    
    if (points === 0) {
        showNotification('該學生沒有完成任何作業，無法加分！', 'error');
        return;
    }
    
    // 儲存加分記錄
    if (!bonusPoints[date]) {
        bonusPoints[date] = {};
    }
    
    bonusPoints[date][seatKey] = {
        points: points,
        subjects: submittedSubjects,
        timestamp: new Date().toISOString()
    };
    
    const student = currentStudents.find(s => s.seatNumber === parseInt(seatNumber));
    saveToLocalStorage();
    renderBonusStudents(date, dateData);
    renderBonusSummary();
    showNotification(`${student.name} 獲得 ${points} 分加分！`, 'success');
}

// 移除個別學生加分
function removeIndividualBonus(date, seatNumber) {
    const seatKey = String(seatNumber);
    
    if (bonusPoints[date] && bonusPoints[date][seatKey]) {
        const points = bonusPoints[date][seatKey].points;
        delete bonusPoints[date][seatKey];
        
        // 如果該日期沒有任何加分記錄，刪除整個日期
        if (Object.keys(bonusPoints[date]).length === 0) {
            delete bonusPoints[date];
        }
        
        const student = currentStudents.find(s => s.seatNumber === parseInt(seatNumber));
        saveToLocalStorage();
        renderBonusStudents(date, currentHomework[date]);
        renderBonusSummary();
        showNotification(`已移除 ${student.name} 的 ${points} 分加分`, 'success');
    }
}

// 全部學生加分
function awardAllBonus() {
    const selectedDate = bonusDate.value;
    if (!selectedDate) {
        showNotification('請先選擇日期！', 'error');
        return;
    }

    const dateData = currentHomework[selectedDate];
    if (!dateData || !dateData.subjects || dateData.subjects.length === 0) {
        showNotification('該日期沒有作業記錄！', 'error');
        return;
    }

    const pointsPerHomework = parseInt(bonusPointsValue.value) || 5;
    let awardedCount = 0;
    let totalPoints = 0;

    if (!bonusPoints[selectedDate]) {
        bonusPoints[selectedDate] = {};
    }

    currentStudents.forEach(student => {
        const seatKey = String(student.seatNumber);
        const submissions = dateData.submissions[seatKey] || {};
        const submittedSubjects = [];
        
        dateData.subjects.forEach(subject => {
            if (submissions[subject]) {
                submittedSubjects.push(subject);
            }
        });
        
        const points = submittedSubjects.length * pointsPerHomework;
        
        if (points > 0 && !bonusPoints[selectedDate][seatKey]) {
            bonusPoints[selectedDate][seatKey] = {
                points: points,
                subjects: submittedSubjects,
                timestamp: new Date().toISOString()
            };
            awardedCount++;
            totalPoints += points;
        }
    });

    if (awardedCount === 0) {
        showNotification('沒有符合加分條件的學生，或所有學生都已加分！', 'error');
        return;
    }

    saveToLocalStorage();
    renderBonusStudents(selectedDate, dateData);
    renderBonusSummary();
    showNotification(`已為 ${awardedCount} 位學生加分，總計 ${totalPoints} 分！`, 'success');
}

// 清除加分記錄
function clearBonusPoints() {
    const selectedDate = bonusDate.value;
    if (!selectedDate) {
        showNotification('請先選擇日期！', 'error');
        return;
    }

    if (!bonusPoints[selectedDate] || Object.keys(bonusPoints[selectedDate]).length === 0) {
        showNotification('該日期沒有加分記錄！', 'error');
        return;
    }

    if (confirm(`確定要清除 ${selectedDate} 的所有加分記錄嗎？`)) {
        delete bonusPoints[selectedDate];
        saveToLocalStorage();
        
        const dateData = currentHomework[selectedDate];
        if (dateData) {
            renderBonusStudents(selectedDate, dateData);
        }
        renderBonusSummary();
        showNotification(`已清除 ${selectedDate} 的所有加分記錄`, 'success');
    }
}

// 渲染加分統計
function renderBonusSummary() {
    const summary = {};
    let totalBonusPoints = 0;
    
    // 計算每位學生的總加分
    currentStudents.forEach(student => {
        const seatKey = String(student.seatNumber);
        let studentTotal = 0;
        let bonusDays = 0;
        
        Object.values(bonusPoints).forEach(dayBonus => {
            if (dayBonus[seatKey]) {
                studentTotal += dayBonus[seatKey].points;
                bonusDays++;
            }
        });
        
        if (studentTotal > 0) {
            summary[student.seatNumber] = {
                name: student.name,
                totalPoints: studentTotal,
                bonusDays: bonusDays
            };
            totalBonusPoints += studentTotal;
        }
    });
    
    const sortedSummary = Object.entries(summary)
        .sort(([,a], [,b]) => b.totalPoints - a.totalPoints);
    
    if (sortedSummary.length === 0) {
        bonusSummaryGrid.innerHTML = '<p style="text-align: center; color: #666; font-size: 18px;">尚無加分記錄</p>';
        return;
    }
    
    bonusSummaryGrid.innerHTML = sortedSummary.map(([seatNumber, data], index) => {
        const rank = index + 1;
        const medals = ['🥇', '🥈', '🥉'];
        const medal = rank <= 3 ? medals[rank - 1] : '🏅';
        
        return `
            <div class="bonus-summary-card rank-${rank}">
                <div class="summary-rank">${medal} 第${rank}名</div>
                <div class="summary-student">
                    <span class="summary-seat">${seatNumber}號</span>
                    <span class="summary-name">${data.name}</span>
                </div>
                <div class="summary-points">
                    <span class="points-big">${data.totalPoints}</span>
                    <span class="points-unit">分</span>
                </div>
                <div class="summary-days">${data.bonusDays} 天獲得加分</div>
            </div>
        `;
    }).join('');
}

// ===== 加分記錄下載功能 =====

// 下載單日加分記錄
function downloadSingleBonusRecord() {
    const selectedDate = bonusDate.value;
    if (!selectedDate) {
        showNotification('請先選擇日期！', 'error');
        return;
    }

    const format = bonusDownloadFormat.value;
    const dateBonus = bonusPoints[selectedDate];
    
    if (!dateBonus || Object.keys(dateBonus).length === 0) {
        showNotification('該日期沒有加分記錄！', 'error');
        return;
    }

    const fileName = `加分記錄_${selectedDate}`;
    
    switch (format) {
        case 'csv':
            downloadBonusAsCSV([{ date: selectedDate, data: dateBonus }], fileName);
            break;
        case 'excel':
            downloadBonusAsExcel([{ date: selectedDate, data: dateBonus }], fileName);
            break;
        case 'json':
            downloadBonusAsJSON({ [selectedDate]: dateBonus }, fileName);
            break;
    }
    
    showNotification(`已下載 ${selectedDate} 的加分記錄！`, 'success');
}

// 下載全部加分記錄
function downloadAllBonusRecords() {
    if (Object.keys(bonusPoints).length === 0) {
        showNotification('尚無加分記錄可下載！', 'error');
        return;
    }

    const format = bonusDownloadFormat.value;
    const fileName = `全部加分記錄_${new Date().toISOString().split('T')[0]}`;
    
    const allRecords = Object.entries(bonusPoints)
        .filter(([date, data]) => Object.keys(data).length > 0)
        .map(([date, data]) => ({ date, data }))
        .sort((a, b) => new Date(b.date) - new Date(a.date));
    
    if (allRecords.length === 0) {
        showNotification('尚無有效的加分記錄可下載！', 'error');
        return;
    }
    
    switch (format) {
        case 'csv':
            downloadBonusAsCSV(allRecords, fileName);
            break;
        case 'excel':
            downloadBonusAsExcel(allRecords, fileName);
            break;
        case 'json':
            downloadBonusAsJSON(bonusPoints, fileName);
            break;
    }
    
    showNotification(`已下載全部加分記錄 (${allRecords.length} 天)！`, 'success');
}

// 下載加分統計報告
function downloadBonusSummaryReport() {
    const format = bonusDownloadFormat.value;
    const fileName = `加分統計報告_${new Date().toISOString().split('T')[0]}`;
    
    // 生成統計報告資料
    const summaryData = generateBonusSummaryData();
    
    if (summaryData.studentSummary.length === 0) {
        showNotification('尚無加分記錄可生成報告！', 'error');
        return;
    }
    
    switch (format) {
        case 'csv':
            downloadBonusSummaryAsCSV(summaryData, fileName);
            break;
        case 'excel':
            downloadBonusSummaryAsExcel(summaryData, fileName);
            break;
        case 'json':
            downloadBonusAsJSON(summaryData, fileName);
            break;
    }
    
    showNotification('已下載加分統計報告！', 'success');
}

// 下載加分記錄為CSV格式
function downloadBonusAsCSV(records, fileName) {
    let csvContent = '\uFEFF'; // UTF-8 BOM for Chinese characters
    csvContent += '日期,座號,學生姓名,獲得分數,完成作業,加分時間\n';
    
    records.forEach(({ date, data }) => {
        const formattedDate = new Date(date).toLocaleDateString('zh-TW');
        Object.entries(data).forEach(([seatNumber, bonusInfo]) => {
            const student = currentStudents.find(s => String(s.seatNumber) === seatNumber);
            const studentName = student ? student.name : `座號${seatNumber}`;
            const subjects = bonusInfo.subjects.join('、');
            const timestamp = new Date(bonusInfo.timestamp).toLocaleString('zh-TW');
            
            csvContent += `${formattedDate},${seatNumber},${studentName},${bonusInfo.points},${subjects},${timestamp}\n`;
        });
    });
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    downloadFile(blob, `${fileName}.csv`);
}

// 下載加分記錄為Excel格式
function downloadBonusAsExcel(records, fileName) {
    let content = '\uFEFF'; // UTF-8 BOM
    
    // 建立摘要表
    content += '加分記錄摘要\n';
    content += '日期,總加分人數,總加分,平均加分\n';
    
    records.forEach(({ date, data }) => {
        const formattedDate = new Date(date).toLocaleDateString('zh-TW');
        const totalStudents = Object.keys(data).length;
        const totalPoints = Object.values(data).reduce((sum, bonus) => sum + bonus.points, 0);
        const averagePoints = totalStudents > 0 ? Math.round(totalPoints / totalStudents) : 0;
        
        content += `${formattedDate},${totalStudents},${totalPoints},${averagePoints}\n`;
    });
    
    content += '\n詳細記錄\n';
    content += '日期,座號,學生姓名,獲得分數,完成作業,加分時間\n';
    
    records.forEach(({ date, data }) => {
        const formattedDate = new Date(date).toLocaleDateString('zh-TW');
        Object.entries(data).forEach(([seatNumber, bonusInfo]) => {
            const student = currentStudents.find(s => String(s.seatNumber) === seatNumber);
            const studentName = student ? student.name : `座號${seatNumber}`;
            const subjects = bonusInfo.subjects.join('、');
            const timestamp = new Date(bonusInfo.timestamp).toLocaleString('zh-TW');
            
            content += `${formattedDate},${seatNumber},${studentName},${bonusInfo.points},${subjects},${timestamp}\n`;
        });
    });
    
    const blob = new Blob([content], { type: 'application/vnd.ms-excel;charset=utf-8;' });
    downloadFile(blob, `${fileName}.xls`);
}

// 下載加分記錄為JSON格式
function downloadBonusAsJSON(data, fileName) {
    const exportData = {
        exportDate: new Date().toISOString(),
        students: currentStudents,
        bonusRecords: data,
        statistics: generateBonusSummaryData()
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json;charset=utf-8;' });
    downloadFile(blob, `${fileName}.json`);
}

// 生成加分統計資料
function generateBonusSummaryData() {
    const studentSummary = [];
    const dateSummary = [];
    let totalBonusPoints = 0;
    let totalBonusCount = 0;
    
    // 學生統計
    currentStudents.forEach(student => {
        const seatKey = String(student.seatNumber);
        let studentTotal = 0;
        let bonusDays = 0;
        let totalSubjects = 0;
        
        Object.entries(bonusPoints).forEach(([date, dayBonus]) => {
            if (dayBonus[seatKey]) {
                studentTotal += dayBonus[seatKey].points;
                bonusDays++;
                totalSubjects += dayBonus[seatKey].subjects.length;
            }
        });
        
        if (studentTotal > 0) {
            studentSummary.push({
                seatNumber: student.seatNumber,
                name: student.name,
                totalPoints: studentTotal,
                bonusDays: bonusDays,
                averagePoints: Math.round(studentTotal / bonusDays),
                totalSubjects: totalSubjects
            });
            totalBonusPoints += studentTotal;
            totalBonusCount++;
        }
    });
    
    // 日期統計
    Object.entries(bonusPoints).forEach(([date, dayBonus]) => {
        const totalStudents = Object.keys(dayBonus).length;
        const totalPoints = Object.values(dayBonus).reduce((sum, bonus) => sum + bonus.points, 0);
        const averagePoints = totalStudents > 0 ? Math.round(totalPoints / totalStudents) : 0;
        
        dateSummary.push({
            date: date,
            totalStudents: totalStudents,
            totalPoints: totalPoints,
            averagePoints: averagePoints
        });
    });
    
    // 排序
    studentSummary.sort((a, b) => b.totalPoints - a.totalPoints);
    dateSummary.sort((a, b) => new Date(b.date) - new Date(a.date));
    
    return {
        summary: {
            totalBonusPoints: totalBonusPoints,
            totalStudentsWithBonus: totalBonusCount,
            totalBonusDays: Object.keys(bonusPoints).length,
            averageBonusPerStudent: totalBonusCount > 0 ? Math.round(totalBonusPoints / totalBonusCount) : 0
        },
        studentSummary: studentSummary,
        dateSummary: dateSummary
    };
}

// 下載統計報告為CSV格式
function downloadBonusSummaryAsCSV(summaryData, fileName) {
    let csvContent = '\uFEFF'; // UTF-8 BOM for Chinese characters
    
    // 總體統計
    csvContent += '加分統計總覽\n';
    csvContent += '項目,數值\n';
    csvContent += `總加分,${summaryData.summary.totalBonusPoints}分\n`;
    csvContent += `獲得加分學生數,${summaryData.summary.totalStudentsWithBonus}人\n`;
    csvContent += `加分天數,${summaryData.summary.totalBonusDays}天\n`;
    csvContent += `平均每人加分,${summaryData.summary.averageBonusPerStudent}分\n\n`;
    
    // 學生排行
    csvContent += '學生加分排行榜\n';
    csvContent += '排名,座號,姓名,總加分,加分天數,平均加分,完成作業數\n';
    summaryData.studentSummary.forEach((student, index) => {
        csvContent += `${index + 1},${student.seatNumber},${student.name},${student.totalPoints},${student.bonusDays},${student.averagePoints},${student.totalSubjects}\n`;
    });
    
    csvContent += '\n日期統計\n';
    csvContent += '日期,加分人數,總加分,平均加分\n';
    summaryData.dateSummary.forEach(dateData => {
        const formattedDate = new Date(dateData.date).toLocaleDateString('zh-TW');
        csvContent += `${formattedDate},${dateData.totalStudents},${dateData.totalPoints},${dateData.averagePoints}\n`;
    });
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    downloadFile(blob, `${fileName}.csv`);
}

// 下載統計報告為Excel格式
function downloadBonusSummaryAsExcel(summaryData, fileName) {
    let content = '\uFEFF'; // UTF-8 BOM
    
    // 總體統計
    content += '加分統計總覽\n';
    content += '項目,數值\n';
    content += `總加分,${summaryData.summary.totalBonusPoints}分\n`;
    content += `獲得加分學生數,${summaryData.summary.totalStudentsWithBonus}人\n`;
    content += `加分天數,${summaryData.summary.totalBonusDays}天\n`;
    content += `平均每人加分,${summaryData.summary.averageBonusPerStudent}分\n\n`;
    
    // 學生排行
    content += '學生加分排行榜\n';
    content += '排名,座號,姓名,總加分,加分天數,平均加分,完成作業數\n';
    summaryData.studentSummary.forEach((student, index) => {
        content += `${index + 1},${student.seatNumber},${student.name},${student.totalPoints},${student.bonusDays},${student.averagePoints},${student.totalSubjects}\n`;
    });
    
    content += '\n日期統計\n';
    content += '日期,加分人數,總加分,平均加分\n';
    summaryData.dateSummary.forEach(dateData => {
        const formattedDate = new Date(dateData.date).toLocaleDateString('zh-TW');
        content += `${formattedDate},${dateData.totalStudents},${dateData.totalPoints},${dateData.averagePoints}\n`;
    });
    
    const blob = new Blob([content], { type: 'application/vnd.ms-excel;charset=utf-8;' });
    downloadFile(blob, `${fileName}.xls`);
}

// ===== 作業追蹤系統 =====
// 作業追蹤相關DOM元素
const trackingDate = document.getElementById('tracking-date');
const loadTrackingDateBtn = document.getElementById('load-tracking-date-btn');
const refreshTrackingBtn = document.getElementById('refresh-tracking-btn');
const totalStudentsCount = document.getElementById('total-students-count');
const totalAssignmentsCount = document.getElementById('total-assignments-count');
const incompleteAssignmentsCount = document.getElementById('incomplete-assignments-count');
const completionRate = document.getElementById('completion-rate');
const studentFilter = document.getElementById('student-filter');
const subjectFilter = document.getElementById('subject-filter');
const statusFilter = document.getElementById('status-filter');
const trackingTableBody = document.getElementById('tracking-table-body');
const trackingEmptyState = document.getElementById('tracking-empty-state');
const markAllCompleteBtn = document.getElementById('mark-all-complete-btn');
const markSelectedCompleteBtn = document.getElementById('mark-selected-complete-btn');
const exportIncompleteBtn = document.getElementById('export-incomplete-btn');
const showIncompleteStudentsBtn = document.getElementById('show-incomplete-students-btn');
const setDeadlineReminderBtn = document.getElementById('set-deadline-reminder-btn');
const generateTrackingReportBtn = document.getElementById('generate-tracking-report-btn');

// 當前追蹤資料
let currentTrackingData = [];
let currentTrackingDate = '';

// 初始化作業追蹤系統
function initializeHomeworkTracking() {
    // 設定預設日期為今天
    const today = new Date().toISOString().split('T')[0];
    trackingDate.value = today;
    currentTrackingDate = today;
    
    // 事件監聽器
    loadTrackingDateBtn.addEventListener('click', loadTrackingData);
    refreshTrackingBtn.addEventListener('click', refreshTrackingData);
    studentFilter.addEventListener('input', filterTrackingData);
    subjectFilter.addEventListener('change', filterTrackingData);
    statusFilter.addEventListener('change', filterTrackingData);
    markAllCompleteBtn.addEventListener('click', markAllComplete);
    markSelectedCompleteBtn.addEventListener('click', markSelectedComplete);
    exportIncompleteBtn.addEventListener('click', exportIncompleteData);
    showIncompleteStudentsBtn.addEventListener('click', showIncompleteStudents);
    setDeadlineReminderBtn.addEventListener('click', setDeadlineReminder);
    generateTrackingReportBtn.addEventListener('click', generateTrackingReport);
    
    // 載入初始資料
    loadTrackingData();
}

// 載入追蹤資料
function loadTrackingData() {
    const selectedDate = trackingDate.value;
    if (!selectedDate) {
        showNotification('請選擇要查看的日期', 'error');
        return;
    }
    
    currentTrackingDate = selectedDate;
    const dayData = currentHomework[selectedDate];
    
    if (!dayData || !dayData.subjects || dayData.subjects.length === 0) {
        showTrackingEmptyState('該日期沒有作業記錄');
        return;
    }
    
    // 更新科目篩選器
    updateSubjectFilter(dayData.subjects);
    
    // 生成追蹤資料
    generateTrackingData(dayData);
    
    // 更新顯示
    updateTrackingSummary();
    renderTrackingTable();
    
    showNotification('追蹤資料載入成功', 'success');
}

// 重新整理追蹤資料
function refreshTrackingData() {
    loadTrackingData();
    showNotification('資料已重新整理', 'success');
}

// 更新科目篩選器
function updateSubjectFilter(subjects) {
    subjectFilter.innerHTML = '<option value="">所有科目</option>';
    subjects.forEach(subject => {
        const option = document.createElement('option');
        option.value = subject;
        option.textContent = subject;
        subjectFilter.appendChild(option);
    });
}

// 生成追蹤資料
function generateTrackingData(dayData) {
    currentTrackingData = [];
    
    currentStudents.forEach(student => {
        const seatKey = student.seatNumber.toString();
        const studentSubmissions = dayData.submissions[seatKey] || {};
        
        dayData.subjects.forEach(subject => {
            const submissionData = studentSubmissions[subject];
            let status, timestamp;
            
            // 處理新舊資料格式
            if (typeof submissionData === 'boolean') {
                status = submissionData;
                timestamp = null;
            } else if (submissionData && typeof submissionData === 'object') {
                status = submissionData.completed || false;
                timestamp = submissionData.timestamp;
            } else {
                status = false;
                timestamp = null;
            }
            
            currentTrackingData.push({
                seatNumber: student.seatNumber,
                name: student.name,
                subject: subject,
                status: status,
                timestamp: timestamp,
                id: `${seatKey}-${subject}`
            });
        });
    });
}

// 更新追蹤摘要
function updateTrackingSummary() {
    const totalStudents = currentStudents.length;
    const totalAssignments = currentTrackingData.length;
    const incompleteAssignments = currentTrackingData.filter(item => !item.status).length;
    const completionRateValue = totalAssignments > 0 ? Math.round(((totalAssignments - incompleteAssignments) / totalAssignments) * 100) : 0;
    
    totalStudentsCount.textContent = totalStudents;
    totalAssignmentsCount.textContent = totalAssignments;
    incompleteAssignmentsCount.textContent = incompleteAssignments;
    completionRate.textContent = `${completionRateValue}%`;
}

// 渲染追蹤表格
function renderTrackingTable() {
    // 應用篩選器
    const filteredData = getFilteredTrackingData();
    
    if (filteredData.length === 0) {
        showTrackingEmptyState('沒有符合條件的記錄');
        return;
    }
    
    hideTrackingEmptyState();
    
    trackingTableBody.innerHTML = '';
    
    filteredData.forEach(item => {
        const row = document.createElement('tr');
        
        // 座號
        const seatCell = document.createElement('td');
        seatCell.innerHTML = `<span class="seat-badge">${item.seatNumber}</span>`;
        row.appendChild(seatCell);
        
        // 姓名
        const nameCell = document.createElement('td');
        nameCell.textContent = item.name;
        row.appendChild(nameCell);
        
        // 科目
        const subjectCell = document.createElement('td');
        subjectCell.textContent = item.subject;
        row.appendChild(subjectCell);
        
        // 狀態
        const statusCell = document.createElement('td');
        const statusBadge = document.createElement('span');
        statusBadge.className = `status-badge ${item.status ? 'completed' : 'incomplete'}`;
        statusBadge.textContent = item.status ? '✅ 已完成' : '❌ 未繳交';
        statusCell.appendChild(statusBadge);
        row.appendChild(statusCell);
        
        // 提交時間
        const timeCell = document.createElement('td');
        if (item.timestamp) {
            const timeDisplay = formatSubmissionTime(item.timestamp);
            timeCell.innerHTML = `<span class="timestamp">${timeDisplay}</span>`;
        } else {
            timeCell.innerHTML = '<span class="timestamp">-</span>';
        }
        row.appendChild(timeCell);
        
        // 操作按鈕
        const actionCell = document.createElement('td');
        if (!item.status) {
            const completeBtn = document.createElement('button');
            completeBtn.className = 'complete-btn';
            completeBtn.innerHTML = '✅ 完成';
            completeBtn.onclick = () => markAsComplete(item);
            actionCell.appendChild(completeBtn);
        } else {
            const undoBtn = document.createElement('button');
            undoBtn.className = 'complete-btn';
            undoBtn.style.background = 'linear-gradient(45deg, #FF6B6B, #FF5252)';
            undoBtn.innerHTML = '↶ 取消';
            undoBtn.onclick = () => markAsIncomplete(item);
            actionCell.appendChild(undoBtn);
        }
        row.appendChild(actionCell);
        
        // 添加複選框用於批量操作
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.className = 'tracking-checkbox';
        checkbox.dataset.id = item.id;
        seatCell.prepend(checkbox);
        
        trackingTableBody.appendChild(row);
    });
}

// 篩選追蹤資料
function getFilteredTrackingData() {
    const studentQuery = studentFilter.value.toLowerCase();
    const selectedSubject = subjectFilter.value;
    const selectedStatus = statusFilter.value;
    
    return currentTrackingData.filter(item => {
        const matchesStudent = !studentQuery || 
            item.seatNumber.toString().includes(studentQuery) || 
            item.name.toLowerCase().includes(studentQuery);
        
        const matchesSubject = !selectedSubject || item.subject === selectedSubject;
        
        const matchesStatus = !selectedStatus || 
            (selectedStatus === 'completed' && item.status) ||
            (selectedStatus === 'incomplete' && !item.status);
        
        return matchesStudent && matchesSubject && matchesStatus;
    });
}

// 應用篩選器並重新渲染
function filterTrackingData() {
    renderTrackingTable();
}

// 標記為完成（包含時間戳記）
function markAsComplete(item) {
    const currentTime = new Date().toISOString();
    
    // 更新localStorage中的資料
    if (!currentHomework[currentTrackingDate]) {
        currentHomework[currentTrackingDate] = { subjects: [], submissions: {} };
    }
    
    const seatKey = item.seatNumber.toString();
    if (!currentHomework[currentTrackingDate].submissions[seatKey]) {
        currentHomework[currentTrackingDate].submissions[seatKey] = {};
    }
    
    currentHomework[currentTrackingDate].submissions[seatKey][item.subject] = {
        completed: true,
        timestamp: currentTime
    };
    
    // 更新學生統計
    const student = currentStudents.find(s => s.seatNumber == item.seatNumber);
    if (student) {
        student.totalSubmissions = (student.totalSubmissions || 0) + 1;
    }
    
    // 儲存資料
    saveToLocalStorage();
    
    // 重新載入並更新顯示
    loadTrackingData();
    
    showNotification(`已標記 ${item.name} 的 ${item.subject} 為完成`, 'success');
}

// 標記為未完成
function markAsIncomplete(item) {
    // 更新localStorage中的資料
    const seatKey = item.seatNumber.toString();
    if (currentHomework[currentTrackingDate] && 
        currentHomework[currentTrackingDate].submissions[seatKey]) {
        
        currentHomework[currentTrackingDate].submissions[seatKey][item.subject] = {
            completed: false,
            timestamp: null
        };
        
        // 更新學生統計
        const student = currentStudents.find(s => s.seatNumber == item.seatNumber);
        if (student && student.totalSubmissions > 0) {
            student.totalSubmissions -= 1;
        }
    }
    
    // 儲存資料
    saveToLocalStorage();
    
    // 重新載入並更新顯示
    loadTrackingData();
    
    showNotification(`已取消 ${item.name} 的 ${item.subject} 完成狀態`, 'success');
}

// 標記全部完成
function markAllComplete() {
    const incompleteItems = currentTrackingData.filter(item => !item.status);
    
    if (incompleteItems.length === 0) {
        showNotification('沒有未完成的作業', 'info');
        return;
    }
    
    if (!confirm(`確定要標記所有 ${incompleteItems.length} 項未完成作業為完成嗎？`)) {
        return;
    }
    
    const currentTime = new Date().toISOString();
    
    incompleteItems.forEach(item => {
        const seatKey = item.seatNumber.toString();
        
        if (!currentHomework[currentTrackingDate]) {
            currentHomework[currentTrackingDate] = { subjects: [], submissions: {} };
        }
        
        if (!currentHomework[currentTrackingDate].submissions[seatKey]) {
            currentHomework[currentTrackingDate].submissions[seatKey] = {};
        }
        
        currentHomework[currentTrackingDate].submissions[seatKey][item.subject] = {
            completed: true,
            timestamp: currentTime
        };
        
        // 更新學生統計
        const student = currentStudents.find(s => s.seatNumber == item.seatNumber);
        if (student) {
            student.totalSubmissions = (student.totalSubmissions || 0) + 1;
        }
    });
    
    // 儲存資料
    saveToLocalStorage();
    
    // 重新載入並更新顯示
    loadTrackingData();
    
    showNotification(`已標記 ${incompleteItems.length} 項作業為完成`, 'success');
}

// 標記選中完成
function markSelectedComplete() {
    const checkboxes = document.querySelectorAll('.tracking-checkbox:checked');
    
    if (checkboxes.length === 0) {
        showNotification('請先選擇要標記的作業', 'error');
        return;
    }
    
    if (!confirm(`確定要標記選中的 ${checkboxes.length} 項作業為完成嗎？`)) {
        return;
    }
    
    const currentTime = new Date().toISOString();
    
    checkboxes.forEach(checkbox => {
        const itemId = checkbox.dataset.id;
        const item = currentTrackingData.find(i => i.id === itemId);
        
        if (item && !item.status) {
            const seatKey = item.seatNumber.toString();
            
            if (!currentHomework[currentTrackingDate]) {
                currentHomework[currentTrackingDate] = { subjects: [], submissions: {} };
            }
            
            if (!currentHomework[currentTrackingDate].submissions[seatKey]) {
                currentHomework[currentTrackingDate].submissions[seatKey] = {};
            }
            
            currentHomework[currentTrackingDate].submissions[seatKey][item.subject] = {
                completed: true,
                timestamp: currentTime
            };
            
            // 更新學生統計
            const student = currentStudents.find(s => s.seatNumber == item.seatNumber);
            if (student) {
                student.totalSubmissions = (student.totalSubmissions || 0) + 1;
            }
        }
    });
    
    // 儲存資料
    saveToLocalStorage();
    
    // 重新載入並更新顯示
    loadTrackingData();
    
    showNotification(`已標記 ${checkboxes.length} 項作業為完成`, 'success');
}

// 匯出未完成作業資料
function exportIncompleteData() {
    const incompleteItems = currentTrackingData.filter(item => !item.status);
    
    if (incompleteItems.length === 0) {
        showNotification('沒有未完成的作業可以匯出', 'info');
        return;
    }
    
    let csvContent = '\uFEFF'; // UTF-8 BOM
    csvContent += '未完成作業清單\n';
    csvContent += `匯出日期：${new Date().toLocaleDateString('zh-TW')}\n`;
    csvContent += `作業日期：${new Date(currentTrackingDate).toLocaleDateString('zh-TW')}\n\n`;
    csvContent += '座號,姓名,科目,狀態\n';
    
    incompleteItems.forEach(item => {
        csvContent += `${item.seatNumber},${item.name},${item.subject},未完成\n`;
    });
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const fileName = `未完成作業_${currentTrackingDate}`;
    downloadFile(blob, `${fileName}.csv`);
    
    showNotification('未完成作業清單已匯出', 'success');
}

// 顯示未完成學生清單
function showIncompleteStudents() {
    const incompleteItems = currentTrackingData.filter(item => !item.status);
    
    if (incompleteItems.length === 0) {
        showNotification('恭喜！所有學生都已完成作業！', 'success');
        return;
    }
    
    // 按學生分組
    const studentGroups = {};
    incompleteItems.forEach(item => {
        if (!studentGroups[item.seatNumber]) {
            studentGroups[item.seatNumber] = {
                name: item.name,
                subjects: []
            };
        }
        studentGroups[item.seatNumber].subjects.push(item.subject);
    });
    
    let message = '未完成作業的學生：\n\n';
    Object.keys(studentGroups).forEach(seatNumber => {
        const student = studentGroups[seatNumber];
        message += `🔸 ${seatNumber}號 ${student.name}：${student.subjects.join('、')}\n`;
    });
    
    alert(message);
}

// 設定截止時間提醒
function setDeadlineReminder() {
    alert('截止時間提醒功能正在開發中，敬請期待！');
}

// 生成追蹤報表
function generateTrackingReport() {
    const reportData = {
        date: currentTrackingDate,
        totalStudents: currentStudents.length,
        totalAssignments: currentTrackingData.length,
        completedAssignments: currentTrackingData.filter(item => item.status).length,
        incompleteAssignments: currentTrackingData.filter(item => !item.status).length,
        completionRate: currentTrackingData.length > 0 ? 
            Math.round((currentTrackingData.filter(item => item.status).length / currentTrackingData.length) * 100) : 0,
        subjects: [...new Set(currentTrackingData.map(item => item.subject))],
        studentSummary: []
    };
    
    // 學生摘要
    const studentStats = {};
    currentTrackingData.forEach(item => {
        if (!studentStats[item.seatNumber]) {
            studentStats[item.seatNumber] = {
                seatNumber: item.seatNumber,
                name: item.name,
                total: 0,
                completed: 0,
                subjects: []
            };
        }
        studentStats[item.seatNumber].total++;
        studentStats[item.seatNumber].subjects.push(item.subject);
        if (item.status) {
            studentStats[item.seatNumber].completed++;
        }
    });
    
    reportData.studentSummary = Object.values(studentStats).map(student => ({
        ...student,
        completionRate: Math.round((student.completed / student.total) * 100)
    }));
    
    // 生成CSV報表
    let csvContent = '\uFEFF'; // UTF-8 BOM
    csvContent += '作業追蹤統計報表\n';
    csvContent += `生成日期：${new Date().toLocaleDateString('zh-TW')}\n`;
    csvContent += `作業日期：${new Date(currentTrackingDate).toLocaleDateString('zh-TW')}\n\n`;
    
    csvContent += '總體統計\n';
    csvContent += '項目,數值\n';
    csvContent += `總學生數,${reportData.totalStudents}\n`;
    csvContent += `總作業數,${reportData.totalAssignments}\n`;
    csvContent += `已完成,${reportData.completedAssignments}\n`;
    csvContent += `未完成,${reportData.incompleteAssignments}\n`;
    csvContent += `完成率,${reportData.completionRate}%\n`;
    csvContent += `科目,${reportData.subjects.join('、')}\n\n`;
    
    csvContent += '學生完成狀況\n';
    csvContent += '座號,姓名,已完成,總數,完成率,科目\n';
    reportData.studentSummary.forEach(student => {
        csvContent += `${student.seatNumber},${student.name},${student.completed},${student.total},${student.completionRate}%,${student.subjects.join('、')}\n`;
    });
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const fileName = `作業追蹤報表_${currentTrackingDate}`;
    downloadFile(blob, `${fileName}.csv`);
    
    showNotification('追蹤報表已生成', 'success');
}

// 顯示空狀態
function showTrackingEmptyState(message) {
    trackingEmptyState.style.display = 'block';
    trackingTableBody.innerHTML = '';
    
    if (message) {
        trackingEmptyState.querySelector('.empty-state-content p').textContent = message;
    }
    
    // 清空摘要
    totalStudentsCount.textContent = '0';
    totalAssignmentsCount.textContent = '0';
    incompleteAssignmentsCount.textContent = '0';
    completionRate.textContent = '0%';
}

// 隐藏空狀態
function hideTrackingEmptyState() {
    trackingEmptyState.style.display = 'none';
}

// ===== 系統重置功能 =====
// 處理系統重置請求
function handleSystemReset() {
    // 密碼驗證
    const password = prompt('🔐 請輸入管理員密碼：');
    
    if (password === null) {
        // 使用者取消操作
        return;
    }
    
    if (password !== '0718') {
        showNotification('❌ 密碼錯誤！', 'error');
        return;
    }
    
    // 密碼正確，進行二次確認
    const confirmation = confirm(
        '⚠️ 警告：此操作將重置系統資料！\n\n' +
        '將被清除的資料：\n' +
        '• 所有作業記錄\n' +
        '• 所有加分記錄\n' +
        '• 所有歷史資料\n' +
        '• 當前設定的科目\n\n' +
        '✅ 學生名單將會保留\n\n' +
        '此操作無法復原，確定要繼續嗎？'
    );
    
    if (!confirmation) {
        showNotification('✅ 已取消重置操作', 'info');
        return;
    }
    
    // 執行系統重置
    performSystemReset();
}

// 執行系統重置
function performSystemReset() {
    try {
        // 保留學生資料，清空其他全域變數
        // currentStudents = []; // 註釋掉，保留學生資料
        currentSubjects = [];
        currentHomework = {};
        homeworkHistory = [];
        bonusPoints = {};
        
        // 清空localStorage（除了學生資料）
        // localStorage.removeItem('students'); // 註釋掉，保留學生資料
        localStorage.removeItem('subjects');
        localStorage.removeItem('currentHomework');
        localStorage.removeItem('homeworkHistory');
        localStorage.removeItem('bonusPoints');
        
        // 重新初始化界面（保留學生相關顯示）
        resetAllDisplays();
        
        // 顯示成功訊息
        showNotification('🎉 系統重置完成！學生名單已保留', 'success');
        
        // 自動切換到登記作業頁面
        switchSection('homework-entry');
        
    } catch (error) {
        console.error('系統重置失敗:', error);
        showNotification('❌ 系統重置失敗，請重新嘗試', 'error');
    }
}

// 重置所有顯示界面
function resetAllDisplays() {
    // 清空科目列表
    subjectsList.innerHTML = '';
    
    // 重新渲染學生選擇下拉選單（保留學生資料）
    renderStudentOptions();
    
    // 清空作業檢查列表
    homeworkChecklist.innerHTML = '';
    
    // 重置提交按鈕
    submitHomeworkBtn.disabled = true;
    
    // 清空狀態網格
    statusGrid.innerHTML = '';
    
    // 重新渲染學生網格（保留學生顯示）
    renderStudentsGrid();
    
    // 清空歷史記錄容器
    const historyContainer = document.getElementById('history-container');
    if (historyContainer) {
        historyContainer.innerHTML = '';
    }
    
    // 重置加分摘要
    const bonusSummaryGrid = document.getElementById('bonus-summary-grid');
    if (bonusSummaryGrid) {
        bonusSummaryGrid.innerHTML = '';
    }
    
    // 重置加分學生網格
    const bonusStudentsGrid = document.getElementById('bonus-students-grid');
    if (bonusStudentsGrid) {
        bonusStudentsGrid.innerHTML = '';
    }
    
    // 重置作業追蹤
    if (typeof showTrackingEmptyState === 'function') {
        showTrackingEmptyState('尚未載入任何資料');
    }
    
    // 重新渲染排行榜（基於保留的學生資料）
    renderLeaderboard();
    
    // 清空科目輸入框
    subjectInput.value = '';
    
    // 重置所有日期選擇器為今天
    const today = new Date().toISOString().split('T')[0];
    if (historyDate) historyDate.value = today;
    if (bonusDate) bonusDate.value = today;
    if (trackingDate) trackingDate.value = today;
    
    console.log('✅ 系統重置完成 - 學生資料已保留，其他界面已重置');
}

// ===== 批次操作功能 =====

// 更新選擇計數顯示
function updateSelectedCount() {
    selectedCount.textContent = selectedStudents.size;
}

// 選擇所有學生
function selectAllStudents() {
    const searchTerm = studentSearch.value.toLowerCase();
    const filteredStudents = currentStudents.filter(student =>
        student.name.toLowerCase().includes(searchTerm) ||
        student.seatNumber.toString().includes(searchTerm)
    );
    
    filteredStudents.forEach(student => {
        selectedStudents.add(student.seatNumber);
    });
    
    // 更新所有複選框狀態
    document.querySelectorAll('.student-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
    
    // 更新學生卡片樣式
    document.querySelectorAll('.student-card').forEach(card => {
        card.classList.add('selected');
    });
    
    updateSelectedCount();
    showNotification(`已選擇所有 ${filteredStudents.length} 位學生`, 'success');
}

// 取消選擇所有學生
function deselectAllStudents() {
    selectedStudents.clear();
    
    // 更新所有複選框狀態
    document.querySelectorAll('.student-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    
    // 更新學生卡片樣式
    document.querySelectorAll('.student-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    updateSelectedCount();
    showNotification('已取消選擇所有學生', 'info');
}

// 切換學生選擇狀態
function toggleStudentSelection(seatNumber) {
    if (selectedStudents.has(seatNumber)) {
        selectedStudents.delete(seatNumber);
    } else {
        selectedStudents.add(seatNumber);
    }
    
    // 更新學生卡片樣式
    const studentCard = document.querySelector(`.student-card[data-seat="${seatNumber}"]`);
    if (studentCard) {
        studentCard.classList.toggle('selected');
    }
    
    updateSelectedCount();
}

// 開啟批次編輯模態框
function openBatchEditModal() {
    if (selectedStudents.size === 0) {
        showNotification('請先選擇要編輯的學生！', 'error');
        return;
    }
    
    batchEditCount.textContent = selectedStudents.size;
    batchPreviewContainer.innerHTML = '';
    batchPreviewContainer.style.display = 'none';
    
    // 重置表單
    batchSeatStart.value = '';
    batchSeatEnd.value = '';
    batchNamePrefix.value = '';
    batchNameSuffix.value = '';
    batchSeatIncrement.value = '1';
    previewBatchChangesBtn.disabled = true;
    saveBatchChangesBtn.disabled = true;
    
    batchEditStudentsModal.classList.add('show');
}

// 關閉批次編輯模態框
function closeBatchEditModal() {
    batchEditStudentsModal.classList.remove('show');
    selectedStudents.clear();
    renderStudentsGrid();
}

// 預覽批次變更
function previewBatchChanges() {
    const seatStart = parseInt(batchSeatStart.value);
    const seatEnd = parseInt(batchSeatEnd.value);
    const namePrefix = batchNamePrefix.value.trim();
    const nameSuffix = batchNameSuffix.value.trim();
    const seatIncrement = parseInt(batchSeatIncrement.value) || 1;
    
    if (!seatStart || !seatEnd || seatStart > seatEnd) {
        showNotification('請輸入有效的座號範圍！', 'error');
        return;
    }
    
    if (!namePrefix && !nameSuffix && seatIncrement === 1) {
        showNotification('請至少設定一項修改內容！', 'error');
        return;
    }
    
    // 生成預覽內容
    let previewHTML = '<div class="batch-preview-content">';
    previewHTML += '<h4>變更預覽</h4>';
    previewHTML += '<div class="preview-table">';
    previewHTML += '<div class="preview-header">';
    previewHTML += '<div>原座號</div><div>原姓名</div><div>新座號</div><div>新姓名</div>';
    previewHTML += '</div>';
    
    const selectedStudentsArray = Array.from(selectedStudents).sort((a, b) => a - b);
    let newSeatNumber = seatStart;
    
    selectedStudentsArray.forEach(seatNumber => {
        const student = currentStudents.find(s => s.seatNumber === seatNumber);
        if (student) {
            const newSeat = newSeatNumber <= seatEnd ? newSeatNumber : seatNumber;
            const newName = `${namePrefix || ''}${student.name}${nameSuffix || ''}`;
            
            previewHTML += '<div class="preview-row">';
            previewHTML += `<div>${seatNumber}</div>`;
            previewHTML += `<div>${student.name}</div>`;
            previewHTML += `<div>${newSeat !== seatNumber ? newSeat : seatNumber}</div>`;
            previewHTML += `<div>${newName !== student.name ? newName : student.name}</div>`;
            previewHTML += '</div>';
            
            if (newSeatNumber <= seatEnd) {
                newSeatNumber += seatIncrement;
            }
        }
    });
    
    previewHTML += '</div></div>';
    batchPreviewContainer.innerHTML = previewHTML;
    batchPreviewContainer.style.display = 'block';
    saveBatchChangesBtn.disabled = false;
}

// 儲存批次變更
function saveBatchChanges() {
    const seatStart = parseInt(batchSeatStart.value);
    const seatEnd = parseInt(batchSeatEnd.value);
    const namePrefix = batchNamePrefix.value.trim();
    const nameSuffix = batchNameSuffix.value.trim();
    const seatIncrement = parseInt(batchSeatIncrement.value) || 1;
    
    if (!seatStart || !seatEnd || seatStart > seatEnd) {
        showNotification('請輸入有效的座號範圍！', 'error');
        return;
    }
    
    if (!namePrefix && !nameSuffix && seatIncrement === 1) {
        showNotification('請至少設定一項修改內容！', 'error');
        return;
    }
    
    // 檢查座號是否會衝突
    const selectedStudentsArray = Array.from(selectedStudents).sort((a, b) => a - b);
    let newSeatNumber = seatStart;
    const seatConflicts = [];
    
    selectedStudentsArray.forEach(seatNumber => {
        if (newSeatNumber <= seatEnd) {
            const existingStudent = currentStudents.find(s =>
                s.seatNumber === newSeatNumber && !selectedStudents.has(s.seatNumber)
            );
            if (existingStudent) {
                seatConflicts.push(newSeatNumber);
            }
            newSeatNumber += seatIncrement;
        }
    });
    
    if (seatConflicts.length > 0) {
        showNotification(`座號 ${seatConflicts.join(', ')} 已被其他學生使用！`, 'error');
        return;
    }
    
    // 執行批次修改
    let modifiedCount = 0;
    newSeatNumber = seatStart;
    
    selectedStudentsArray.forEach(seatNumber => {
        const studentIndex = currentStudents.findIndex(s => s.seatNumber === seatNumber);
        if (studentIndex > -1) {
            const student = currentStudents[studentIndex];
            const oldSeatNumber = student.seatNumber;
            const oldName = student.name;
            
            // 更新座號
            if (newSeatNumber <= seatEnd && newSeatNumber !== oldSeatNumber) {
                student.seatNumber = newSeatNumber;
                updateHomeworkRecordsForSeatChange(oldSeatNumber, newSeatNumber);
                modifiedCount++;
            }
            
            // 更新姓名
            const newName = `${namePrefix || ''}${student.name}${nameSuffix || ''}`;
            if (newName !== oldName) {
                student.name = newName;
                modifiedCount++;
            }
            
            if (newSeatNumber <= seatEnd) {
                newSeatNumber += seatIncrement;
            }
        }
    });
    
    // 重新排序學生列表
    currentStudents.sort((a, b) => a.seatNumber - b.seatNumber);
    
    // 儲存並更新界面
    saveToLocalStorage();
    renderStudentOptions();
    renderStudentsGrid();
    renderStatusGrid();
    
    closeBatchEditModal();
    showNotification(`已成功修改 ${modifiedCount} 項學生資料`, 'success');
}

// 批次刪除學生
function batchDeleteStudents() {
    if (selectedStudents.size === 0) {
        showNotification('請先選擇要刪除的學生！', 'error');
        return;
    }
    
    if (!confirm(`確定要刪除選中的 ${selectedStudents.size} 位學生嗎？此操作無法復原！`)) {
        return;
    }
    
    const deletedCount = selectedStudents.size;
    
    // 執行批次刪除
    selectedStudents.forEach(seatNumber => {
        const index = currentStudents.findIndex(s => s.seatNumber === seatNumber);
        if (index > -1) {
            currentStudents.splice(index, 1);
        }
    });
    
    // 清空選擇
    selectedStudents.clear();
    
    // 儲存並更新界面
    saveToLocalStorage();
    renderStudentOptions();
    renderStudentsGrid();
    renderStatusGrid();
    
    showNotification(`已成功刪除 ${deletedCount} 位學生`, 'success');
}