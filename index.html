<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>班級作業追蹤系統</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Main Container -->
    <div class="container">
        <!-- Left Sidebar Navigation -->
        <nav class="sidebar">
            <div class="logo">
                <h2>📚 作業小幫手</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item active" data-section="homework-entry">
                    <button class="nav-btn">
                        <span class="icon">✏️</span>
                        <span class="text">登記作業</span>
                    </button>
                </li>
                <li class="nav-item" data-section="student-list">
                    <button class="nav-btn">
                        <span class="icon">👥</span>
                        <span class="text">學生名單</span>
                    </button>
                </li>
                <li class="nav-item" data-section="homework-history">
                    <button class="nav-btn">
                        <span class="icon">📊</span>
                        <span class="text">作業記錄</span>
                    </button>
                </li>
                <li class="nav-item" data-section="bonus-points">
                    <button class="nav-btn">
                        <span class="icon">⭐</span>
                        <span class="text">加分系統</span>
                    </button>
                </li>
                <li class="nav-item" data-section="homework-tracking">
                    <button class="nav-btn">
                        <span class="icon">📋</span>
                        <span class="text">作業追蹤</span>
                    </button>
                </li>
            </ul>
            
            <!-- Leaderboard -->
            <div class="leaderboard">
                <h3>🏆 本週排行榜</h3>
                <div class="leaderboard-list" id="leaderboard-list">
                    <!-- Leaderboard items will be inserted here -->
                </div>
            </div>
        </nav>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Homework Entry Section -->
            <section id="homework-entry" class="content-section active">
                <div class="section-header">
                    <h1>📝 今日作業登記</h1>
                    <p class="date-display" id="current-date"></p>
                    <!-- 系統重置按鈕移到右上角 -->
                    <button id="reset-system-btn" class="btn-reset-subtle" title="系統重置">⚙️ 重置</button>
                </div>

                <!-- Teacher Section: Add Homework -->
                <div class="teacher-section">
                    <h2>🍎 老師區域</h2>
                    <div class="homework-form">
                        <div class="form-group">
                            <label for="subject-input">科目：</label>
                            <input type="text" id="subject-input" placeholder="請輸入科目名稱">
                            <button id="add-subject-btn" class="btn-primary">新增科目</button>
                        </div>
                        <div class="subjects-list" id="subjects-list">
                            <!-- Dynamic subjects will be added here -->
                        </div>
                    </div>
                </div>

                <!-- Student Section: Submit Homework -->
                <div class="student-section">
                    <h2>🎒 學生區域</h2>
                    <div class="student-form">
                        <div class="form-group">
                            <label for="seat-select">座號：</label>
                            <select id="seat-select">
                                <option value="">請選擇座號</option>
                            </select>
                        </div>
                        <div class="homework-checklist" id="homework-checklist">
                            <!-- Homework checklist will be populated here -->
                        </div>
                        <button id="submit-homework-btn" class="btn-success" disabled>提交作業</button>
                    </div>
                </div>

                <!-- Status Display -->
                <div class="status-section">
                    <h3>📋 今日繳交狀況</h3>
                    <div class="status-grid" id="status-grid">
                        <!-- Status will be displayed here -->
                    </div>
                </div>

                <!-- Encouragement Messages -->
                <div class="encouragement" id="encouragement-message">
                    <!-- Random encouragement messages will appear here -->
                </div>
            </section>

            <!-- Student List Section -->
            <section id="student-list" class="content-section">
                <div class="section-header">
                    <h1>👥 學生名單管理</h1>
                </div>

                <!-- Add Student Form -->
                <div class="add-student-form">
                    <h2>➕ 新增學生</h2>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="new-seat-number">座號：</label>
                            <input type="number" id="new-seat-number" min="1" max="50" placeholder="座號">
                        </div>
                        <div class="form-group">
                            <label for="new-student-name">姓名：</label>
                            <input type="text" id="new-student-name" placeholder="學生姓名">
                        </div>
                        <button id="add-student-btn" class="btn-primary">新增學生</button>
                    </div>
                </div>

                <!-- Batch Operations Section -->
                <div class="batch-operations-section">
                    <h2>⚡ 批次操作</h2>
                    <div class="batch-controls">
                        <div class="form-row">
                            <div class="form-group">
                                <button id="select-all-students-btn" class="btn-outline">全選</button>
                                <button id="deselect-all-students-btn" class="btn-outline">取消全選</button>
                            </div>
                            <div class="form-group">
                                <button id="batch-edit-students-btn" class="btn-primary" disabled>✏️ 批次修改</button>
                                <button id="batch-delete-students-btn" class="btn-danger" disabled>🗑️ 批次刪除</button>
                            </div>
                        </div>
                        <div class="selection-info">
                            <span id="selected-count">已選擇 0 位學生</span>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="search-section">
                    <div class="form-group">
                        <label for="student-search">🔍 搜尋學生：</label>
                        <input type="text" id="student-search" placeholder="輸入座號或姓名搜尋">
                    </div>
                </div>

                <!-- Students Grid -->
                <div class="students-grid" id="students-grid">
                    <!-- Student cards will be displayed here -->
                </div>
    
                <!-- Batch Edit Students Modal -->
                <div class="modal" id="batch-edit-students-modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>✏️ 批次修改學生資料</h3>
                            <button class="modal-close" id="close-batch-edit-modal">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="batch-edit-info">
                                <p><strong>已選擇學生數量：</strong><span id="batch-edit-count">0</span> 位</p>
                                <p style="color: #666; font-size: 14px;">💡 留空表示不修改該欄位</p>
                            </div>
                            
                            <div class="batch-edit-options">
                                <div class="form-group">
                                    <label for="batch-seat-start">座號範圍起始：</label>
                                    <input type="number" id="batch-seat-start" min="1" max="50" placeholder="起始座號（可選）">
                                </div>
                                <div class="form-group">
                                    <label for="batch-seat-end">座號範圍結束：</label>
                                    <input type="number" id="batch-seat-end" min="1" max="50" placeholder="結束座號（可選）">
                                </div>
                                <div class="form-group">
                                    <label for="batch-name-prefix">姓名前綴：</label>
                                    <input type="text" id="batch-name-prefix" placeholder="新增前綴（可選）">
                                </div>
                                <div class="form-group">
                                    <label for="batch-name-suffix">姓名後綴：</label>
                                    <input type="text" id="batch-name-suffix" placeholder="新增後綴（可選）">
                                </div>
                                <div class="form-group">
                                    <label for="batch-seat-increment">座號遞增：</label>
                                    <input type="number" id="batch-seat-increment" min="1" max="10" value="1" placeholder="遞增值">
                                </div>
                            </div>
    
                            <div class="batch-preview-section">
                                <h4>📋 預覽結果</h4>
                                <div class="batch-preview-container" id="batch-preview-container">
                                    <!-- 預覽結果將顯示在這裡 -->
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button id="preview-batch-changes-btn" class="btn-secondary">👁️ 預覽變更</button>
                            <button id="save-batch-changes-btn" class="btn-success" disabled>💾 儲存變更</button>
                            <button id="cancel-batch-edit-btn" class="btn-secondary">❌ 取消</button>
                        </div>
                    </div>
                </div>
                </section>

            <!-- Edit Student Modal -->
            <div class="modal" id="edit-student-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>✏️ 修改學生資料</h3>
                        <button class="modal-close" id="close-edit-modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="edit-seat-number">座號：</label>
                            <input type="number" id="edit-seat-number" min="1" max="50">
                        </div>
                        <div class="form-group">
                            <label for="edit-student-name">姓名：</label>
                            <input type="text" id="edit-student-name">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button id="save-student-btn" class="btn-success">💾 儲存修改</button>
                        <button id="cancel-edit-btn" class="btn-secondary">❌ 取消</button>
                    </div>
                </div>
            </div>

            <!-- Edit Submission Status Modal -->
            <div class="modal" id="edit-submission-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>✏️ 修改繳交狀況</h3>
                        <button class="modal-close" id="close-submission-modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="student-info-display">
                            <p><strong>學生：</strong><span id="edit-submission-student-name"></span></p>
                            <p><strong>日期：</strong><span id="edit-submission-date"></span></p>
                        </div>
                        <div class="form-group">
                            <label>作業繳交狀況：</label>
                            <div class="subject-checkboxes" id="edit-submission-subjects">
                                <!-- 動態生成作業科目複選框 -->
                            </div>
                        </div>
                        <div class="edit-submission-note">
                            <p style="color: #666; font-size: 14px;">
                                💡 勾選表示該科目已繳交，取消勾選表示未繳交
                            </p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button id="save-submission-btn" class="btn-success">💾 儲存修改</button>
                        <button id="cancel-submission-btn" class="btn-secondary">❌ 取消</button>
                    </div>
                </div>
            </div>
            </section>

            <!-- Homework History Section -->
            <section id="homework-history" class="content-section">
                <div class="section-header">
                    <h1>📊 作業記錄查詢</h1>
                </div>

                <!-- Date Filter -->
                <div class="filter-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="history-date">選擇日期：</label>
                            <input type="date" id="history-date">
                        </div>
                        <div class="form-group">
                            <button id="filter-history-btn" class="btn-primary">查詢</button>
                        </div>
                    </div>
                    
                    <!-- Download Section -->
                    <div class="download-section">
                        <h3>📥 下載記錄</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="download-format">下載格式：</label>
                                <select id="download-format">
                                    <option value="csv">CSV格式</option>
                                    <option value="excel">Excel格式</option>
                                    <option value="json">JSON格式</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <button id="download-single-btn" class="btn-success">📥 下載當日記錄</button>
                            </div>
                            <div class="form-group">
                                <button id="download-all-btn" class="btn-success">📥 下載全部記錄</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- History Display -->
                <div class="history-container" id="history-container">
                    <!-- History records will be displayed here -->
                </div>
            </section>

            <!-- Bonus Points Section -->
            <section id="bonus-points" class="content-section">
                <div class="section-header">
                    <h1>⭐ 加分系統</h1>
                    <p class="date-display">準時交作業，每份加5分！</p>
                </div>

                <!-- Bonus Settings -->
                <div class="bonus-settings-section">
                    <h2>🎯 加分設定</h2>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="bonus-date">選擇日期：</label>
                            <input type="date" id="bonus-date">
                        </div>
                        <div class="form-group">
                            <label for="bonus-points-value">每份作業加分：</label>
                            <input type="number" id="bonus-points-value" value="5" min="1" max="20">
                        </div>
                        <div class="form-group">
                            <button id="load-bonus-date-btn" class="btn-primary">📅 載入日期</button>
                        </div>
                    </div>
                </div>

                <!-- Bonus Award Section -->
                <div class="bonus-award-section">
                    <h2>🏆 加分評定</h2>
                    <div class="bonus-students-grid" id="bonus-students-grid">
                        <!-- Bonus students will be displayed here -->
                    </div>
                    <div class="bonus-actions">
                        <button id="award-all-btn" class="btn-success">⭐ 全部加分</button>
                        <button id="clear-bonus-btn" class="btn-secondary">🔄 清除加分</button>
                    </div>
                </div>

                <!-- Bonus Summary -->
                <div class="bonus-summary-section">
                    <h2>📊 加分統計</h2>
                    <div class="bonus-summary-grid" id="bonus-summary-grid">
                        <!-- Bonus summary will be displayed here -->
                    </div>
                </div>

                <!-- Download Bonus Records Section -->
                <div class="bonus-download-section">
                    <h2>📥 下載加分記錄</h2>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="bonus-download-format">下載格式：</label>
                            <select id="bonus-download-format">
                                <option value="csv">CSV格式</option>
                                <option value="excel">Excel格式</option>
                                <option value="json">JSON格式</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <button id="download-single-bonus-btn" class="btn-success">📥 下載當日加分</button>
                        </div>
                        <div class="form-group">
                            <button id="download-all-bonus-btn" class="btn-success">📥 下載全部加分</button>
                        </div>
                        <div class="form-group">
                            <button id="download-bonus-summary-btn" class="btn-primary">📊 下載統計報告</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Homework Tracking Section -->
            <section id="homework-tracking" class="content-section">
                <div class="section-header">
                    <h1>📋 作業追蹤管理</h1>
                    <p class="date-display">追蹤學生未繳交作業，快速完成登記</p>
                </div>

                <!-- Date Filter -->
                <div class="tracking-filter-section">
                    <h2>📅 選擇查看日期</h2>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="tracking-date">查看日期：</label>
                            <input type="date" id="tracking-date">
                        </div>
                        <div class="form-group">
                            <button id="load-tracking-date-btn" class="btn-primary">📋 載入追蹤</button>
                        </div>
                        <div class="form-group">
                            <button id="refresh-tracking-btn" class="btn-secondary">🔄 重新整理</button>
                        </div>
                    </div>
                </div>

                <!-- Tracking Summary -->
                <div class="tracking-summary-section">
                    <h2>📊 追蹤摘要</h2>
                    <div class="summary-cards" id="tracking-summary-cards">
                        <div class="summary-card">
                            <div class="summary-icon">👥</div>
                            <div class="summary-content">
                                <div class="summary-number" id="total-students-count">0</div>
                                <div class="summary-label">總學生數</div>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="summary-icon">📚</div>
                            <div class="summary-content">
                                <div class="summary-number" id="total-assignments-count">0</div>
                                <div class="summary-label">總作業數</div>
                            </div>
                        </div>
                        <div class="summary-card incomplete">
                            <div class="summary-icon">❌</div>
                            <div class="summary-content">
                                <div class="summary-number" id="incomplete-assignments-count">0</div>
                                <div class="summary-label">未繳交</div>
                            </div>
                        </div>
                        <div class="summary-card completion">
                            <div class="summary-icon">📈</div>
                            <div class="summary-content">
                                <div class="summary-number" id="completion-rate">0%</div>
                                <div class="summary-label">完成率</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tracking Table -->
                <div class="tracking-table-section">
                    <h2>📋 學生作業追蹤表</h2>
                    <div class="tracking-controls">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="student-filter">🔍 篩選學生：</label>
                                <input type="text" id="student-filter" placeholder="輸入座號或姓名搜尋">
                            </div>
                            <div class="form-group">
                                <label for="subject-filter">📚 篩選科目：</label>
                                <select id="subject-filter">
                                    <option value="">所有科目</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="status-filter">📊 篩選狀態：</label>
                                <select id="status-filter">
                                    <option value="">所有狀態</option>
                                    <option value="incomplete">未繳交</option>
                                    <option value="completed">已完成</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="tracking-table-container">
                        <table class="tracking-table" id="tracking-table">
                            <thead>
                                <tr>
                                    <th>座號</th>
                                    <th>姓名</th>
                                    <th>作業科目</th>
                                    <th>狀態</th>
                                    <th>提交時間</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="tracking-table-body">
                                <!-- 追蹤資料將動態插入此處 -->
                            </tbody>
                        </table>
                    </div>

                    <div class="tracking-empty-state" id="tracking-empty-state" style="display: none;">
                        <div class="empty-state-content">
                            <div class="empty-state-icon">📝</div>
                            <h3>沒有找到相關記錄</h3>
                            <p>請選擇有作業記錄的日期，或調整篩選條件</p>
                        </div>
                    </div>
                </div>

                <!-- Batch Operations -->
                <div class="batch-operations-section">
                    <h2>⚡ 批量操作</h2>
                    <div class="batch-actions">
                        <div class="form-row">
                            <div class="form-group">
                                <button id="mark-all-complete-btn" class="btn-success">✅ 標記全部完成</button>
                            </div>
                            <div class="form-group">
                                <button id="mark-selected-complete-btn" class="btn-primary">✅ 標記選中完成</button>
                            </div>
                            <div class="form-group">
                                <button id="export-incomplete-btn" class="btn-secondary">📥 匯出未完成</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Panel -->
                <div class="quick-actions-section">
                    <h2>🚀 快速操作</h2>
                    <div class="quick-actions-grid">
                        <div class="quick-action-card">
                            <div class="quick-action-icon">📞</div>
                            <div class="quick-action-content">
                                <h3>未交學生清單</h3>
                                <p>查看今日未繳交作業的學生</p>
                                <button id="show-incomplete-students-btn" class="btn-outline">查看清單</button>
                            </div>
                        </div>
                        <div class="quick-action-card">
                            <div class="quick-action-icon">⏰</div>
                            <div class="quick-action-content">
                                <h3>逾期提醒</h3>
                                <p>設定作業截止時間提醒</p>
                                <button id="set-deadline-reminder-btn" class="btn-outline">設定提醒</button>
                            </div>
                        </div>
                        <div class="quick-action-card">
                            <div class="quick-action-icon">📊</div>
                            <div class="quick-action-content">
                                <h3>統計報表</h3>
                                <p>生成詳細的作業統計報表</p>
                                <button id="generate-tracking-report-btn" class="btn-outline">生成報表</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Notification System -->
    <div class="notification" id="notification">
        <span class="notification-text"></span>
        <button class="notification-close">&times;</button>
    </div>

    <!-- JavaScript -->
    <script src="script.js"></script>
</body>
</html>